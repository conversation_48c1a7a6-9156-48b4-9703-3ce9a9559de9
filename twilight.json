{"name": {"ar": "افق", "en": "ofoq"}, "repository": "https://github.com/fady-eng/wahg", "author_email": "ehabrashed<PERSON><PERSON>@gmail.com", "features": ["mega-menu", "fonts", "color", "breadcrumb", "unite-cards-height", "component-featured-products", "component-fixed-banner", "component-fixed-products", "component-products-slider", "component-photos-slider", "component-parallax-background", "component-testimonials", "component-square-photos", "component-store-features", "component-youtube", "menu-images", "filters"], "settings": [{"id": "squar_photo_bg_image_size", "type": "items", "format": "dropdown-list", "label": "طريقة عرض الصور في قسم قائمة عناصر ", "description": null, "labelHTML": null, "icon": "sicon-list", "selected": [{"label": "اظهار الصورة كاملةً في المنتصف (Contain)", "value": "contain", "key": "f4adec13-6e4c-654b-b978-b2807935ced9"}], "options": [{"label": "تغطية الصورة كامل المساحة مع المحافظة على أبعاد الصورة (Cover)", "value": "cover", "key": "e9c8ea8f-c5c6-234e-935-3bce269afde4"}, {"label": "اظهار الصورة كاملةً في المنتصف (Contain)", "value": "contain", "key": "f4adec13-6e4c-654b-b978-b2807935ced9"}], "source": "Manual", "required": true}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "وضع عمودي للمنتجات في مربع المنتجات الثابتة في الصفحة الرئيسية", "description": null, "id": "vertical_fixed_products", "format": "switch", "required": false, "value": false, "selected": false}, {"type": "boolean", "id": "is_more_button_enabled", "label": "عرض (زر الكل) في الصفحة الرئيسية", "format": "switch"}, {"type": "static", "id": "static-line1", "format": "line"}, {"type": "static", "format": "title", "id": "static-label2", "value": "خيارات أعلى الصفحة", "variant": "h6"}, {"id": "header_is_sticky", "label": "تثبت القائمة الرئيسية أعلى الصفحة عند التمرير لأسفل", "description": "يُنصح بالغاء تفعيلها في حالة وجود عناصر كثيرة في القائمة المنسدلة", "type": "boolean", "icon": "sicon-toggle-off", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "شريط علوي داكن", "description": null, "id": "topnav_is_dark", "format": "switch", "required": false, "value": true, "selected": false}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "عرض روابط الصفحات الهامة في الشريط العلوي", "description": "مثل صفحة الهبوط ، المدونة ،الصفحات التعريفية", "id": "important_links", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "static", "id": "static-line3", "format": "line"}, {"type": "static", "format": "title", "id": "static-label4", "value": "خيارات أسفل الصفحة", "variant": "h6"}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "الوضع الداكن", "description": null, "id": "footer_is_dark", "format": "switch", "required": false, "value": true, "selected": false}, {"type": "static", "id": "static-line5", "format": "line"}, {"type": "static", "format": "title", "id": "static-label6", "value": "خيارات صفحة المنتج", "variant": "h6"}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تثبيت زر الإضافة والكمية أسفل شاشة الجوال", "description": null, "id": "sticky_add_to_cart", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "اظهار الوسوم", "description": null, "id": "show_tags", "format": "switch", "required": false, "value": true, "selected": true}, {"id": "navbar_logo_size", "type": "items", "format": "dropdown-list", "label": "حجم اللوجو في شريط التنقل", "description": "اختر الحجم المناسب لعرض اللوجو في شريط التنقل العلوي", "labelHTML": null, "icon": "sicon-image", "selected": [{"label": "متوسط (120px)", "value": "medium", "key": "navbar-logo-medium"}], "options": [{"label": "صغير (80px)", "value": "small", "key": "navbar-logo-small"}, {"label": "متوسط (120px)", "value": "medium", "key": "navbar-logo-medium"}, {"label": "كبير (160px)", "value": "large", "key": "navbar-logo-large"}, {"label": "كبير جداً (200px)", "value": "extra-large", "key": "navbar-logo-extra-large"}], "source": "Manual", "required": true}, {"id": "navbar_logo_size", "type": "items", "format": "dropdown-list", "label": "حجم اللوجو في شريط التنقل", "description": "اختر الحجم المناسب لعرض اللوجو في شريط التنقل العلوي", "labelHTML": null, "icon": "sicon-image", "selected": [{"label": "متوسط (120px)", "value": "medium", "key": "navbar-logo-medium"}], "options": [{"label": "صغير (80px)", "value": "small", "key": "navbar-logo-small"}, {"label": "متوسط (120px)", "value": "medium", "key": "navbar-logo-medium"}, {"label": "كبير (160px)", "value": "large", "key": "navbar-logo-large"}, {"label": "كبير جداً (200px)", "value": "extra-large", "key": "navbar-logo-extra-large"}], "source": "Manual", "required": true}, {"id": "slider_background_size", "type": "items", "format": "dropdown-list", "label": "طريقة عرض الصور في سليدر صور المنتج", "description": null, "labelHTML": null, "icon": "sicon-list", "selected": [{"label": "اظهار الصورة كاملةً في المنتصف (Contain)", "value": "contain", "key": "f4adec13-6e4c-433b-b141-b2807935ced9"}], "options": [{"label": "تغطية الصورة كامل المساحة مع المحافظة على أبعاد الصورة (Cover)", "value": "cover", "key": "e9c8ea8f-c5c6-438e-9d45-3bce269afde4"}, {"label": "اظهار الصورة كاملةً في المنتصف (Contain)", "value": "contain", "key": "f4adec13-6e4c-433b-b141-b2807935ced9"}], "source": "Manual", "required": true}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تكبير الصور في سليدر صور المنتج", "description": "هذه الخاصية تمكنك من تكبير الصورة لتسهل رؤية المزيد من التفاصيل بها", "id": "imageZoom", "format": "switch", "required": false, "value": false, "selected": false}, {"type": "static", "id": "static-line-whatsapp", "format": "line"}, {"type": "static", "format": "title", "id": "static-label-whatsapp", "value": "إعدادات أيقونة الواتساب", "variant": "h6"}, {"id": "whatsapp_enabled", "type": "boolean", "format": "switch", "label": "تفعيل أيقونة الواتساب", "description": "إظهار أيقونة الواتساب العائمة على الموقع", "icon": "sicon-whatsapp", "value": false, "selected": false, "required": false}, {"id": "whatsapp_number", "type": "string", "format": "text", "label": "رقم الواتساب", "description": "أدخل رقم الواتساب مع رمز الدولة (مثال: 966501234567)", "icon": "sicon-phone", "value": "", "placeholder": "966501234567", "required": false, "conditions": [{"id": "whatsapp_enabled", "operation": "=", "value": true}]}, {"id": "whatsapp_position", "type": "items", "format": "dropdown-list", "label": "موضع الأيقونة", "description": "اختر موضع ظهور أيقونة الواتساب على الشاشة", "labelHTML": null, "icon": "sicon-cursor-move", "selected": [{"label": "يمين", "value": "right", "key": "whatsapp-position-right"}], "options": [{"label": "يمين", "value": "right", "key": "whatsapp-position-right"}, {"label": "يسار", "value": "left", "key": "whatsapp-position-left"}], "source": "Manual", "required": false, "conditions": [{"id": "whatsapp_enabled", "operation": "=", "value": true}]}, {"id": "whatsapp_display_pages", "type": "items", "format": "dropdown-list", "label": "صفحات العرض", "description": "اختر الصفحات التي ستظهر فيها أيقونة الواتساب", "labelHTML": null, "icon": "sicon-layout-grid", "selected": [{"label": "جميع الصفحات", "value": "all_pages", "key": "whatsapp-display-all"}], "options": [{"label": "جميع الصفحات", "value": "all_pages", "key": "whatsapp-display-all"}, {"label": "الصفحة الرئيسية فقط", "value": "homepage_only", "key": "whatsapp-display-homepage"}], "source": "Manual", "required": false, "conditions": [{"id": "whatsapp_enabled", "operation": "=", "value": true}]}, {"type": "static", "id": "static-line-loading-screen", "format": "line"}, {"type": "static", "format": "title", "id": "static-label-loading-screen", "value": "إعدادات شاشة التحميل", "variant": "h6"}, {"id": "loading_screen_enabled", "type": "boolean", "format": "switch", "label": "تفعيل شاشة التحميل", "description": "إظهار شاشة تحميل مخصصة عند تحميل الصفحات", "icon": "sicon-loading", "value": true, "selected": true, "required": false}, {"id": "loading_screen_duration", "type": "items", "format": "dropdown-list", "label": "مدة عرض شاشة التحميل", "description": "اختر المدة الزمنية لعرض شاشة التحميل", "labelHTML": null, "icon": "sicon-clock", "selected": [{"label": "متوسط (2 ثانية)", "value": "2000", "key": "loading-duration-medium"}], "options": [{"label": "سريع (1 ثانية)", "value": "1000", "key": "loading-duration-fast"}, {"label": "متوسط (2 ثانية)", "value": "2000", "key": "loading-duration-medium"}, {"label": "بطيء (3 ثواني)", "value": "3000", "key": "loading-duration-slow"}, {"label": "تلقائي (ح<PERSON><PERSON> اكتمال التحميل)", "value": "auto", "key": "loading-duration-auto"}], "source": "Manual", "required": false, "conditions": [{"id": "loading_screen_enabled", "operation": "=", "value": true}]}, {"id": "loading_screen_logo", "type": "string", "format": "image", "label": "شعار شاشة التحميل", "description": "اختر صورة الشعار التي ستظهر في شاشة التحميل (اختياري)", "icon": "sicon-image", "value": "", "placeholder": "رفع صورة الشعار", "required": false, "conditions": [{"id": "loading_screen_enabled", "operation": "=", "value": true}]}, {"id": "loading_screen_background_color", "type": "string", "format": "color", "label": "لون خلفية شاشة التحميل", "description": "اختر لون خلفية شاشة التحميل", "icon": "sicon-color-palette", "value": "#ffffff", "required": false, "conditions": [{"id": "loading_screen_enabled", "operation": "=", "value": true}]}, {"id": "loading_screen_background_image", "type": "string", "format": "image", "label": "صورة خلفية شاشة التحميل", "description": "اختر صورة خلفية لشاشة التحميل (اختياري)", "icon": "sicon-image", "value": "", "placeholder": "رفع صورة الخلفية", "required": false, "conditions": [{"id": "loading_screen_enabled", "operation": "=", "value": true}]}, {"id": "loading_screen_display_scope", "type": "items", "format": "dropdown-list", "label": "نطاق العرض", "description": "اختر الصفحات التي ستظهر فيها شاشة التحميل", "labelHTML": null, "icon": "sicon-layout-grid", "selected": [{"label": "جميع الصفحات", "value": "all_pages", "key": "loading-display-all"}], "options": [{"label": "جميع الصفحات", "value": "all_pages", "key": "loading-display-all"}, {"label": "الصفحة الرئيسية فقط", "value": "homepage_only", "key": "loading-display-homepage"}, {"label": "صفحات المنتجات فقط", "value": "product_pages", "key": "loading-display-products"}], "source": "Manual", "required": false, "conditions": [{"id": "loading_screen_enabled", "operation": "=", "value": true}]}, {"id": "loading_screen_animation_type", "type": "items", "format": "dropdown-list", "label": "نوع الحركة", "description": "اختر نوع الحركة المتحركة لشاشة التحميل", "labelHTML": null, "icon": "sicon-refresh", "selected": [{"label": "دوران (Spinner)", "value": "spinner", "key": "loading-animation-spinner"}], "options": [{"label": "دوران (Spinner)", "value": "spinner", "key": "loading-animation-spinner"}, {"label": "تلاشي (Fade)", "value": "fade", "key": "loading-animation-fade"}, {"label": "نقاط متحركة (Dots)", "value": "dots", "key": "loading-animation-dots"}, {"label": "شريط التقدم (Progress Bar)", "value": "progress", "key": "loading-animation-progress"}, {"label": "نبضات (Pulse)", "value": "pulse", "key": "loading-animation-pulse"}], "source": "Manual", "required": false, "conditions": [{"id": "loading_screen_enabled", "operation": "=", "value": true}]}, {"id": "loading_screen_text", "type": "string", "format": "text", "label": "نص شاشة التحميل", "description": "النص الذي سيظهر أسفل الحركة المتحركة (اختياري)", "icon": "sicon-format-text-alt", "value": "جاري التحميل...", "placeholder": "أدخل النص هنا...", "required": false, "multilanguage": true, "minLength": 0, "maxLength": 100, "conditions": [{"id": "loading_screen_enabled", "operation": "=", "value": true}]}, {"id": "loading_screen_text_color", "type": "string", "format": "color", "label": "لون النص", "description": "لون النص في شاشة التحميل", "icon": "sicon-format-text-alt", "value": "#333333", "required": false, "conditions": [{"id": "loading_screen_enabled", "operation": "=", "value": true}]}, {"id": "loading_screen_spinner_color", "type": "string", "format": "color", "label": "لون الحركة المتحركة", "description": "لون الحركة المتحركة في شاشة التحميل", "icon": "sicon-refresh", "value": "#5bd5c4", "required": false, "conditions": [{"id": "loading_screen_enabled", "operation": "=", "value": true}]}], "components": [{"key": "186b3f4f-25cf-4d3c-abca-cef7eed6f0ab", "title": "صور متحركة (محسنة)", "icon": "sicon-image-carousel", "image": "https://cdn.salla.sa/mQgZlG/K36XDnXG3odwQs7Xzy68IpM2wNuJ7uTImfBInlQ8.png", "path": "home.enhanced-slider", "is_default": true, "fields": [{"type": "static", "format": "description", "id": "static-desc", "value": "<div style='margin: -24px -20px 20px'><img class='w-full' src='https://cdn.salla.network/images/themes/raed/preview-images/images-slider-enhancement.png?v=1.1'></div>"}, {"type": "static", "format": "description", "id": "enhance-slider-note", "value": "<div style='background-color: #ddedff;color: #0a5a90;padding: 16px 20px;font-size: 14px;border-radius: 4px; margin: 0 0 30px;'>هذا العنصر غير مناسب لعرض البنرات، يمكنك استخدام عنصر <strong>صور متحركة</strong> بدلاً منه لتظهر صورة البنر بشكل كامل</div>"}, {"id": "slides", "type": "collection", "format": "collection", "required": true, "minLength": 1, "maxLength": 10, "label": null, "item_label": "صورة", "value": [{"slides.image": "https://cdn.salla.sa/ZrBEO/MopzhoqpTkDObcKc8Q8n77AuydanfsGXdZO7iinH.jpg", "slides.title": "روِّج لمنتجاتك كصور متحركة", "slides.description": "اعرض المنتجات التي تود إبرازها لزوّار متجرك في هذه المساحة"}, {"slides.image": "https://cdn.salla.sa/ZrBEO/3gOhYSaIhaHxhH2IHWxJYDl9JHwxP6dGKiZFSq4K.jpg", "slides.title": "شجّع زوار متجرك على الشراء عبر عروض خاصة", "slides.description": "اعرض المنتجات التي تود إبرازها لزوّار متجرك في هذه المساحة"}], "fields": [{"type": "string", "icon": "sicon-image", "value": null, "id": "slides.image", "label": "صورة البنر", "format": "image", "required": true, "placeholder": null, "description": "* المقاس المناسب للصورة هو 900×600 بكسل"}, {"type": "string", "icon": "sicon-format-text-alt", "label": "عنوان رئيسي", "multilanguage": true, "id": "slides.title", "value": null, "required": false, "format": "text", "description": "يتم عرضه على الصورة بحجم بارز جداً، مع إضافة تأثير دخول للنص يضيف لمسة جمالية على معرض الصور.", "placeholder": "يمكنك إضافة عنوان بارز هنا...", "hide": false, "minLength": 0, "maxLength": 100}, {"type": "string", "icon": "sicon-typography", "label": "نص إضافي", "multilanguage": true, "id": "slides.description", "value": null, "description": "يتم عرضه على الصورة بحجم أصغر من العنوان الرئيسي، مع إضافة تأثير دخول للنص يضيف لمسة جمالية على معرض الصور.", "format": "textarea", "required": false, "placeholder": "يمكنك إضافة تفاصيل إضافية هنا", "minLength": 0, "maxLength": 255}]}]}, {"key": "2b1b130b-5b37-422a-9683-e0fd367460c0", "title": "روابط سريعة", "icon": "sicon-layout-grid-rearrange", "path": "home.main-links", "image": "https://cdn.salla.sa/mQgZlG/c3AwZKb12ZRFFfK3vE0vaZYTePnFAUOiZA4dQBg1.png", "is_default": true, "fields": [{"type": "static", "format": "description", "id": "with-bg", "value": "<div style='margin: -24px -20px 20px'><img class='w-full' src='https://cdn.salla.network/images/themes/raed/main-links-with-bg.jpg?v=1.1'></div>", "conditions": [{"id": "merge_with_top_component", "operation": "=", "value": true}]}, {"type": "static", "format": "description", "id": "without-bg", "value": "<div style='padding-top:10px;margin-bottom:30px;border:1px solid #ebebeb;border-radius:5px;text-align:center;overflow:hidden'><h6 style='margin-bottom:10px'>معاينة العنصر</h6><img class='w-full' src='https://cdn.salla.network/images/themes/raed/preview-images/main-links.png?v=1.1'></div>", "conditions": [{"id": "merge_with_top_component", "operation": "=", "value": false}]}, {"type": "string", "icon": "sicon-format-text-alt", "label": "العنوان", "multilanguage": true, "id": "title", "value": null, "required": false, "format": "text", "description": null, "placeholder": "أدخل العنوان هنا...", "minLength": 0, "maxLength": 100}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "دمج مع العنصر السابق", "description": null, "id": "merge_with_top_component", "format": "switch", "required": false, "value": false, "selected": false}, {"type": "static", "format": "description", "id": "static-desc", "value": "<small style='margin: -36px 0 30px;display: block;'>* إزاحة كامل العنصر ومحتوياته مع العنصر السابق، وجعلهما يظهران كعنصر واحد متناسق، يكون ذا فائدة في حالة كان العنصر السابق هو (صور متحركة محسنة). </small>"}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "عرض أسهم التحرك", "description": "", "id": "show_controls", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "عرض روابط التصنيفات", "description": "يمكنك تحديد التصنيفات بدل العناصر الخاصة، مع امكانية عرض صورة التصنيف مع العنصر", "id": "show_cats", "format": "switch", "required": false, "value": false, "selected": false}, {"id": "categories", "type": "items", "format": "dropdown-list", "label": "<PERSON><PERSON><PERSON> التصنيفات", "labelHTML": null, "icon": "sicon-keyboard_arrow_down", "selected": [], "options": [], "source": "categories", "multichoice": true, "searchable": true, "required": false, "conditions": [{"id": "show_cats", "operation": "=", "value": true}]}, {"id": "links", "type": "collection", "format": "collection", "required": true, "minLength": 3, "maxLength": 100, "item_label": "رابط ", "value": [{"links.icon": "sicon-packed-box", "links.title": "منتج جاهز", "links.url__type": "offers_link"}, {"links.icon": "sicon-fabric-swatch", "links.title": "خد<PERSON>ة حسب الطلب", "links.url__type": "offers_link"}, {"links.icon": "sicon-cake", "links.title": "أكل ومشروبات", "links.url__type": "offers_link"}, {"links.icon": "sicon-game-controller-alt", "links.title": "منتج رقمي", "links.url__type": "offers_link"}, {"links.icon": "sicon-barcode-scan", "links.title": "بطاقة رقمية", "links.url__type": "offers_link"}, {"links.icon": "sicon-inbox-full", "links.title": "مجموعة منتجات", "links.url__type": "offers_link"}, {"links.icon": "sicon-calendar-date", "links.title": "حجوزات", "links.url__type": "offers_link"}], "fields": [{"type": "string", "icon": "sicon-format-text-alt", "label": "عنوان الرابط", "id": "links.icon", "value": "sicon-store2", "required": false, "format": "icon", "class": "form--inline", "description": null}, {"type": "string", "icon": "sicon-format-text-alt", "multilanguage": true, "id": "links.title", "value": null, "required": false, "format": "text", "description": null, "placeholder": "أدخل عنوان للرابط هنا...", "minLength": 2, "maxLength": 80}, {"type": "items", "icon": "sicon-link", "label": "الرابط", "id": "links.url", "value": null, "description": null, "required": false, "format": "variable-list", "searchable": true, "source": "custom", "sources": [{"label": "منتج", "key": "products", "value": "products"}, {"label": "تصنيف", "key": "categories", "value": "categories"}, {"label": "ماركة تجارية", "key": "brands", "value": "brands"}, {"label": "صفحة تعريفية", "key": "pages", "value": "pages"}, {"label": "مقالة", "key": "blog_articles", "value": "blog_articles"}, {"label": "تصنيف ض<PERSON>ن المدونة", "key": "blog_categories", "value": "blog_categories"}, {"label": "التخفيضات", "key": "offers_link", "value": "offers_link"}, {"label": "الماركات التجارية", "key": "brands_link", "value": "brands_link"}, {"label": "المدونة", "key": "blog_link", "value": "blog_link"}, {"label": "را<PERSON><PERSON> خارجي", "key": "custom", "value": "custom"}]}], "conditions": [{"id": "show_cats", "operation": "=", "value": false}]}]}, {"key": "9a758d20-2ce4-4782-91fe-c04466464588", "title": "منتجات متحركة مع خلفية", "icon": "sicon-list-play", "path": "home.slider-products-with-header", "image": "https://cdn.salla.sa/mQgZlG/3hhreht06MIPFWepyuz42rg3bHqGZIYmDVs1PjYc.png", "fields": [{"type": "static", "format": "description", "id": "with-bg", "value": "<div style='margin: -24px -20px 20px'><img class='w-full' src='https://cdn.salla.network/images/themes/raed/preview-images/slider-products-with-bg.png?v=1.1'></div>"}, {"type": "string", "icon": "sicon-image", "value": "https://cdn.salla.sa/form-builder/D7bG3pgSit43TheOejBOZV7LIfG1y1Cj0hWLmgIh.jpg", "label": "صورة الخلفية", "id": "background", "format": "image", "required": true, "placeholder": null, "description": "* المقاس المناسب للصورة هو 1233×500 بكسل", "settings": {"height": 580, "width": 1400}}, {"type": "string", "icon": "sicon-format-text-alt", "label": "عنوان رئيسي", "multilanguage": true, "id": "title", "value": null, "required": false, "format": "text", "description": null, "placeholder": "يمكنك إضافة العنوان الرئيسي هنا...", "hide": false, "minLength": 0, "maxLength": "80"}, {"type": "string", "icon": "sicon-typography", "label": "نص توضيحي", "multilanguage": true, "id": "description", "value": null, "description": null, "format": "textarea", "required": false, "placeholder": "يمكنك إضافة وصف لهذا القسم هنا...", "minLength": 0, "maxLength": "256"}, {"type": "items", "icon": "sicon-keyboard_arrow_down", "label": "منتجات", "id": "products", "format": "dropdown-list", "description": null, "selected": [], "options": [], "required": true, "multichoice": true, "source": "products", "searchable": true, "maxLength": 8, "minLength": 1, "value": []}, {"type": "items", "icon": "sicon-link", "label": "رابط عرض الكل", "id": "display_all_url", "value": [], "description": "عند إختيار رابط فارغ سيتم إخفاء زر 'عرض الكل'", "required": false, "format": "variable-list", "searchable": true, "source": "custom", "sources": [{"label": "منتج", "key": "products", "value": "products"}, {"label": "تصنيف", "key": "categories", "value": "categories"}, {"label": "ماركة تجارية", "key": "brands", "value": "brands"}, {"label": "صفحة تعريفية", "key": "pages", "value": "pages"}, {"label": "مقالة", "key": "blog_articles", "value": "blog_articles"}, {"label": "تصنيف ض<PERSON>ن المدونة", "key": "blog_categories", "value": "blog_categories"}, {"label": "التخفيضات", "key": "offers_link", "value": "offers_link"}, {"label": "الماركات التجارية", "key": "brands_link", "value": "brands_link"}, {"label": "المدونة", "key": "blog_link", "value": "blog_link"}, {"label": "را<PERSON><PERSON> خارجي", "key": "custom", "value": "custom"}]}]}, {"key": "b89edc4a-ce0b-468f-8323-2da48147bb32", "title": "صور مربعة (محسنة)", "icon": "sicon-image", "path": "home.enhanced-square-banners", "image": "https://cdn.salla.sa/mQgZlG/VSk26LArCczWj085xH8jxuusMiKzrcE1wsVC6pLm.png", "is_default": true, "fields": [{"type": "static", "format": "description", "id": "with-bg", "value": "<div style='margin: -24px -20px 20px'><img class='w-full' src='https://cdn.salla.network/images/themes/raed/preview-images/square-images.png?v=1.1'></div>"}, {"id": "banners", "type": "collection", "format": "collection", "required": true, "minLength": 1, "maxLength": 5, "label": "قائمة الصور", "item_label": "صورة ", "value": [{"banners.image": "https://cdn.salla.sa/mQgZlG/pzOCEw10wCfXeVSpKnL0Ifd1O9ZkTfnZ17RkE14R.jpg", "banners.url": "", "banners.title": "تصفَّح حسب التصنيف", "banners.description": "سهِّل تجربة التسوُّق والوصول لمنتجاتك"}, {"banners.image": "https://cdn.salla.sa/mQgZlG/lkyDjYwvWJNmAQoGnLALLcUkm66rCCDbTyVlSGdH.jpg", "banners.url": "", "banners.title": "تصفَّح حسب التصنيف", "banners.description": "سهِّل تجربة التسوُّق والوصول لمنتجاتك"}, {"banners.image": "https://cdn.salla.sa/mQgZlG/lkyDjYwvWJNmAQoGnLALLcUkm66rCCDbTyVlSGdH.jpg", "banners.url": "", "banners.title": "تصفَّح حسب التصنيف", "banners.description": "سهِّل تجربة التسوُّق والوصول لمنتجاتك"}, {"banners.image": "https://cdn.salla.sa/mQgZlG/lkyDjYwvWJNmAQoGnLALLcUkm66rCCDbTyVlSGdH.jpg", "banners.url": "", "banners.title": "تصفَّح حسب التصنيف", "banners.description": "سهِّل تجربة التسوُّق والوصول لمنتجاتك"}, {"banners.image": "https://cdn.salla.sa/mQgZlG/lkyDjYwvWJNmAQoGnLALLcUkm66rCCDbTyVlSGdH.jpg", "banners.url": "", "banners.title": "تصفَّح حسب التصنيف", "banners.description": "سهِّل تجربة التسوُّق والوصول لمنتجاتك"}], "fields": [{"type": "string", "icon": "sicon-image", "value": null, "label": "صورة البنر", "id": "banners.image", "format": "image", "required": true, "placeholder": "", "description": "* المقاس المناسب للصورة هو 640×427 بكسل", "settings": {"height": 427, "width": 640}}, {"type": "items", "icon": "sicon-link", "label": "الرابط", "id": "banners.url", "value": null, "description": "", "required": false, "format": "variable-list", "searchable": true, "source": "custom", "sources": [{"label": "منتج", "key": "products", "value": "products"}, {"label": "تصنيف", "key": "categories", "value": "categories"}, {"label": "ماركة تجارية", "key": "brands", "value": "brands"}, {"label": "صفحة تعريفية", "key": "pages", "value": "pages"}, {"label": "مقالة", "key": "blog_articles", "value": "blog_articles"}, {"label": "تصنيف ض<PERSON>ن المدونة", "key": "blog_categories", "value": "blog_categories"}, {"label": "التخفيضات", "key": "offers_link", "value": "offers_link"}, {"label": "الماركات التجارية", "key": "brands_link", "value": "brands_link"}, {"label": "المدونة", "key": "blog_link", "value": "blog_link"}, {"label": "را<PERSON><PERSON> خارجي", "key": "custom", "value": "custom"}]}, {"type": "string", "icon": "sicon-format-text-alt", "label": "عنوان رئيسي", "multilanguage": true, "id": "banners.title", "value": null, "required": false, "format": "text", "description": "نص يتم إضافته على الصورة بنمط خط كبير", "placeholder": "أدخل النص هنا...", "hide": false, "minLength": 0, "maxLength": 100}, {"type": "string", "icon": "sicon-typography", "label": "نص توضيحي", "multilanguage": true, "id": "banners.description", "value": null, "description": "نص يتم إضافته على الصورة بخط أصغر من العنوان الرئيسي", "format": "textarea", "required": false, "placeholder": "Your text here", "minLength": 0, "maxLength": 255}]}]}, {"key": "25f6cf26-a53f-4954-9b32-739b311b32c7", "title": "الماركات التجارية", "icon": "sicon-award-ribbon", "path": "home.brands", "image": "https://cdn.salla.sa/mQgZlG/kvFhVeuUyjK4nHUovBQBZ632Hb6gKV2BXZwid40e.png", "fields": [{"type": "static", "format": "description", "id": "with-bg", "value": "<div style='margin: -24px -20px 20px'><img class='w-full' src='https://cdn.salla.network/images/themes/raed/preview-images/brands.png?v=1.1'></div>"}, {"type": "string", "icon": "sicon-format-text-alt", "label": "العنوان", "multilanguage": true, "id": "title", "required": false, "format": "text", "description": "", "placeholder": "أدخل العنوان هنا...", "minLength": 0, "maxLength": 200, "value": {"ar": "تصفح من خلال العلامات التجارية", "en": "Browse All Brands"}}, {"type": "items", "icon": "sicon-keyboard_arrow_down", "label": "الماركات التجارية", "id": "brands", "format": "dropdown-list", "description": "", "selected": [], "options": [], "required": true, "multichoice": true, "searchable": true, "source": "brands"}]}, {"key": "541cc423-90c7-4230-8a33-a0342cfde4ad", "title": "آراء عملاء مخصصة", "icon": "sicon-chat-bubbles", "path": "home.custom-testimonials", "image": "https://cdn.salla.sa/mQgZlG/JMJiEx1KVn7mzxo5FdtsVsAmpWPM8UJW0i0B93c0.png", "is_default": true, "fields": [{"type": "static", "format": "description", "id": "static-desc", "value": "<div style='margin: -24px -20px 20px'><img class='w-full' src='https://cdn.salla.network/images/themes/raed/preview-images/custom-testimonials.png?v=1.1'></div>"}, {"id": "items", "key": "0473eb46-c7f5-44c5-bd6f-b1fe716a5e64", "type": "collection", "format": "collection", "label": "التقييمات", "description": null, "labelHTML": null, "item_label": "التقييم", "icon": "sicon-list-add", "value": [{"items.name": "ج<PERSON><PERSON><PERSON> عبد الله ", "items.avatar": "https://cdn.assets.salla.network/prod/stores/themes/default/assets/images/avatar_male.png", "items.stars": 4, "items.text": "تجربة تسوق رائعة مع متجركم، المنتجات عالية الجودة والأسعار منافسة. خدمة العملاء ممتازة والتوصيل سريع. أنصح الجميع بالتسوق من هذا المتجر."}, {"items.name": "ج<PERSON><PERSON><PERSON> عبد الله ", "items.avatar": "https://cdn.assets.salla.network/prod/stores/themes/default/assets/images/avatar_male.png", "items.stars": 3, "items.text": "متجر مميز بكل المقاييس، التنوع في المنتجات كبير والأسعار معقولة. التوصيل وصل في الوقت المحدد والمنتجات مطابقة للوصف."}, {"items.name": "ج<PERSON><PERSON><PERSON> عبد الله ", "items.avatar": "https://cdn.assets.salla.network/prod/stores/themes/default/assets/images/avatar_male.png", "items.stars": 5, "items.text": "أفضل متجر اشتريت منه، جودة المنتجات فائقة والأسعار مناسبة. خدمة العملاء متعاونة جداً والتوصيل سريع. سأعود للتسوق مرة أخرى."}, {"items.name": "ج<PERSON><PERSON><PERSON> عبد الله ", "items.avatar": "https://cdn.assets.salla.network/prod/stores/themes/default/assets/images/avatar_male.png", "items.stars": 5, "items.text": "تجربة تسوق مميزة جداً، المنتجات متنوعة والأسعار معقولة. التوصيل سريع والمنتجات مطابقة للوصف. أنصح الجميع بالتسوق من هذا المتجر."}], "fields": [{"id": "items.name", "key": "04e34d04-d1c3-4b75-b1d6-4e5b4ed22623", "type": "string", "format": "text", "label": "اسم العميل", "description": null, "labelHTML": null, "placeholder": "اسم العميل", "icon": "sicon-format-text-alt", "value": null, "multilanguage": false, "required": false}, {"id": "items.avatar", "key": "9ae4a6cc-7d20-4ae9-8bc0-33b2452502ca", "type": "string", "format": "image", "label": "صورة العميل", "description": "المقاس المناسب للصورة هو 68*68 بكسل", "labelHTML": null, "placeholder": "e.g. https://hostname.com/image.png", "icon": "sicon-image", "value": null, "required": true}, {"id": "items.stars", "type": "number", "format": "integer", "label": "<PERSON><PERSON><PERSON> النجوم", "description": null, "key": "f0caf793-88f8-4178-b1cc-a7ace5c09ff6", "labelHTML": "من 1 إلى 5", "placeholder": "القيمة", "icon": "sicon-pencil-ruler", "value": 5, "required": false, "minimum": 1, "maximum": 5}, {"id": "items.text", "key": "0d474cc4-39f6-4495-8e24-a8eb10d4bbab", "type": "string", "format": "textarea", "label": "نص التقييم", "description": null, "labelHTML": null, "placeholder": "نص التقييم", "icon": "sicon-format-text-alt", "value": null, "multilanguage": true, "required": false}], "required": false, "minLength": 1, "maxLength": 30}]}, {"key": "692f0378-de59-4920-b65a-af696abbd8ac", "title": "معرض مع صور مصغرة", "icon": "sicon-packed-box", "path": "home.firstGallary", "fields": [{"type": "static", "format": "title", "id": "static-title-content", "value": "إعدادات المحتوى", "variant": "h6"}, {"id": "banner", "type": "string", "format": "image", "label": "صورة الخلفية أو البنر", "description": "* المقاس المناسب للصورة هو 1200×400 بكسل", "labelHTML": null, "placeholder": "مثال: https://hostname.com/image.png", "icon": "sicon-image", "value": null, "required": false}, {"id": "title", "type": "string", "format": "text", "label": "عنوان المعرض", "description": null, "labelHTML": null, "placeholder": "أدخل عنوان المعرض هنا...", "icon": "sicon-format-text-alt", "value": null, "multilanguage": true, "required": false, "minLength": 6, "maxLength": 30}, {"id": "description", "type": "string", "format": "text", "label": "وصف المعرض", "description": null, "labelHTML": null, "placeholder": "أدخل وصف المعرض هنا...", "icon": "sicon-format-text-alt", "value": null, "multilanguage": true, "required": false, "minLength": 6, "maxLength": 30}, {"id": "icon", "type": "collection", "format": "collection", "label": "الصور المصغرة", "description": null, "labelHTML": null, "item_label": "صورة مصغرة", "icon": "sicon-list-add", "fields": [{"id": "icon.img", "type": "string", "format": "image", "label": "الصورة المصغرة", "description": "* المقاس المناسب للصورة هو 150×150 بكسل", "labelHTML": null, "placeholder": "مثال: https://hostname.com/image.png", "icon": "sicon-image", "value": null, "required": false}, {"id": "icon.url", "type": "items", "format": "variable-list", "label": "رابط الصورة المصغرة", "description": "اختر الرابط الذي ستوجه إليه الصورة المصغرة عند النقر عليها", "labelHTML": null, "placeholder": null, "icon": "sicon-link", "value": null, "required": false, "searchable": true, "source": "custom", "sources": [{"label": "منتج", "key": "products", "value": "products"}, {"label": "تصنيف", "key": "categories", "value": "categories"}, {"label": "ماركة تجارية", "key": "brands", "value": "brands"}, {"label": "صفحة تعريفية", "key": "pages", "value": "pages"}, {"label": "مقالة", "key": "blog_articles", "value": "blog_articles"}, {"label": "تصنيف ض<PERSON>ن المدونة", "key": "blog_categories", "value": "blog_categories"}, {"label": "التخفيضات", "key": "offers_link", "value": "offers_link"}, {"label": "الماركات التجارية", "key": "brands_link", "value": "brands_link"}, {"label": "المدونة", "key": "blog_link", "value": "blog_link"}, {"label": "را<PERSON><PERSON> خارجي", "key": "custom", "value": "custom"}]}], "value": [], "minLength": 1, "maxLength": 10}, {"type": "static", "format": "title", "id": "static-title-layout", "value": "خيارات التخطيط", "variant": "h6"}, {"id": "gallery_layout", "type": "items", "format": "dropdown-list", "label": "تخطيط المعرض", "description": "اختر نوع تخطيط عرض الصور", "labelHTML": null, "icon": "sicon-grid", "selected": [{"label": "شبكة منتظمة (Grid)", "value": "grid", "key": "gallery-layout-grid"}], "options": [{"label": "شبكة منتظمة (Grid)", "value": "grid", "key": "gallery-layout-grid"}, {"label": "شبكة متدرجة (Masonry)", "value": "masonry", "key": "gallery-layout-masonry"}, {"label": "عرض دائري (Carousel)", "value": "carousel", "key": "gallery-layout-carousel"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "columns_count", "type": "number", "format": "integer", "inputType": "number", "label": "ع<PERSON><PERSON> الأعمدة", "description": "عد<PERSON> الأعمدة في الشبكة", "labelHTML": null, "placeholder": "<PERSON><PERSON><PERSON><PERSON> عدد الأعمدة (2-6)", "icon": "sicon-columns", "value": 4, "required": false, "minimum": 2, "maximum": 6}, {"id": "image_aspect_ratio", "type": "items", "format": "dropdown-list", "label": "نسبة أبعاد الصورة", "description": "اختر نسبة العرض إلى الارتفاع للصور", "labelHTML": null, "icon": "sicon-aspect-ratio", "selected": [{"label": "مربع (1:1)", "value": "square", "key": "aspect-ratio-square"}], "options": [{"label": "مربع (1:1)", "value": "square", "key": "aspect-ratio-square"}, {"label": "<PERSON><PERSON><PERSON><PERSON> (16:9)", "value": "landscape", "key": "aspect-ratio-landscape"}, {"label": "عمودي (9:16)", "value": "portrait", "key": "aspect-ratio-portrait"}, {"label": "تلقائي", "value": "auto", "key": "aspect-ratio-auto"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "image_spacing", "type": "number", "format": "integer", "inputType": "number", "label": "المسافة بين الصور", "description": "المسافة بين الصور بالبكسل", "labelHTML": null, "placeholder": "أد<PERSON>ل المسافة (5-50 بكسل)", "icon": "sicon-spacing", "value": 15, "required": false, "minimum": 5, "maximum": 50}, {"type": "static", "format": "title", "id": "static-title-visual", "value": "التأثيرات البصرية", "variant": "h6"}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تفعيل تأثيرات التفاعل", "description": "إضافة تأثيرات عند تمرير الماوس فوق الصور", "id": "enable_hover_effects", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "string", "icon": "sicon-palette", "label": "لون طبقة التفاعل", "id": "hover_overlay_color", "value": "#000000", "required": false, "format": "color", "description": "اختر لون الطبقة التي تظهر عند التفاعل"}, {"id": "hover_overlay_opacity", "type": "number", "format": "integer", "inputType": "number", "label": "شفافية طبقة التفاعل", "description": "شفافية الطبقة عند التفاعل (0-100%)", "labelHTML": null, "placeholder": "أدخل نسبة الشفافية (0-100)", "icon": "sicon-opacity", "value": 30, "required": false, "minimum": 0, "maximum": 100}, {"id": "image_border_radius", "type": "number", "format": "integer", "inputType": "number", "label": "انحناء حواف الصور", "description": "انحناء حواف الصور بالبكسل", "labelHTML": null, "placeholder": "أدخل انحناء الحواف (0-25 بكسل)", "icon": "sicon-border-radius", "value": 8, "required": false, "minimum": 0, "maximum": 25}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تفعيل ظلال الصور", "description": "إضافة ظلال حول الصور", "id": "enable_image_shadows", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "static", "format": "title", "id": "static-title-animation", "value": "إعدادات الحركة", "variant": "h6"}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تفعيل حركات الدخول", "description": "إضافة حركات عند ظهور الصور", "id": "enable_entrance_animations", "format": "switch", "required": false, "value": true, "selected": true}, {"id": "animation_type", "type": "items", "format": "dropdown-list", "label": "نوع الحركة", "description": "اختر نوع حركة دخول الصور", "labelHTML": null, "icon": "sicon-refresh", "selected": [{"label": "تلاشي (FadeIn)", "value": "fadeIn", "key": "animation-type-fadeIn"}], "options": [{"label": "تلاشي (FadeIn)", "value": "fadeIn", "key": "animation-type-fadeIn"}, {"label": "انزلاق للأعلى (SlideUp)", "value": "slideUp", "key": "animation-type-slideUp"}, {"label": "تكبير (ScaleIn)", "value": "scaleIn", "key": "animation-type-scaleIn"}, {"label": "ارتداد (BounceIn)", "value": "bounceIn", "key": "animation-type-bounceIn"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "animation_delay", "type": "items", "format": "dropdown-list", "label": "تأخير الحركة", "description": "اختر مدة تأخير بدء الحركة", "labelHTML": null, "icon": "sicon-clock", "selected": [{"label": "قصير", "value": "short", "key": "animation-delay-short"}], "options": [{"label": "بدون تأخير", "value": "none", "key": "animation-delay-none"}, {"label": "قصير", "value": "short", "key": "animation-delay-short"}, {"label": "متوسط", "value": "medium", "key": "animation-delay-medium"}, {"label": "طويل", "value": "long", "key": "animation-delay-long"}], "source": "Manual", "required": false, "multichoice": false}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تدرج الحركات", "description": "تطبيق الحركات بشكل متدرج على الصور", "id": "stagger_animations", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "static", "format": "title", "id": "static-title-responsive", "value": "الإعدادات المتجاوبة", "variant": "h6"}, {"id": "mobile_columns", "type": "number", "format": "integer", "inputType": "number", "label": "عد<PERSON> الأعمدة في الجوال", "description": "عدد الأعمدة في الأجهزة المحمولة", "labelHTML": null, "placeholder": "<PERSON><PERSON><PERSON><PERSON> عدد الأعمدة للجوال (1-3)", "icon": "sicon-mobile", "value": 2, "required": false, "minimum": 1, "maximum": 3}, {"id": "tablet_columns", "type": "number", "format": "integer", "inputType": "number", "label": "عد<PERSON> الأعمدة في التابلت", "description": "عدد الأعمدة في الأجهزة اللوحية", "labelHTML": null, "placeholder": "<PERSON><PERSON><PERSON><PERSON> عدد الأعمدة للتابلت (2-4)", "icon": "sicon-tablet", "value": 3, "required": false, "minimum": 2, "maximum": 4}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "إخفاء في الجوال", "description": "إخفاء المعرض في الأجهزة المحمولة", "id": "hide_on_mobile", "format": "switch", "required": false, "value": false, "selected": false}, {"type": "static", "format": "title", "id": "static-title-performance", "value": "خيارات الأداء", "variant": "h6"}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "التحميل التدريجي", "description": "تحميل الصور عند الحاجة لتحسين الأداء", "id": "lazy_loading", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تحسين الصور", "description": "تحسين جودة وحجم الصور تلقائياً", "id": "image_optimization", "format": "switch", "required": false, "value": true, "selected": true}]}, {"key": "4823a8ef-36a7-458f-945c-20828d63d442", "title": "خط فاصل مميز", "icon": "sicon-hr", "path": "home.lineBreak", "fields": [{"type": "static", "format": "title", "id": "static-title-style", "value": "إعدادات نمط الخط", "variant": "h6"}, {"id": "line_style", "type": "items", "format": "dropdown-list", "label": "نمط الخط", "description": "اختر نمط الخط الفاصل", "labelHTML": null, "icon": "sicon-hr", "selected": [{"label": "متدرج مضيء (Gaming)", "value": "gaming", "key": "line-style-gaming"}], "options": [{"label": "متدرج مضيء (Gaming)", "value": "gaming", "key": "line-style-gaming"}, {"label": "<PERSON><PERSON> مستقيم", "value": "solid", "key": "line-style-solid"}, {"label": "<PERSON>ط متقطع", "value": "dashed", "key": "line-style-dashed"}, {"label": "<PERSON><PERSON> منقط", "value": "dotted", "key": "line-style-dotted"}, {"label": "<PERSON><PERSON> مزدوج", "value": "double", "key": "line-style-double"}], "source": "Manual", "required": false, "multichoice": false}, {"type": "string", "icon": "sicon-palette", "label": "لون الخط الأساسي", "id": "line_color", "value": "#1de9b6", "required": false, "format": "color", "description": "اختر لون الخط الفاصل"}, {"type": "string", "icon": "sicon-palette", "label": "لون الخط الثانوي", "id": "line_secondary_color", "value": "#4cc9f0", "required": false, "format": "color", "description": "اختر اللون الثانوي للتدرج (للنمط المضيء فقط)"}, {"id": "line_thickness", "type": "number", "format": "integer", "inputType": "number", "label": "سماكة الخط", "description": "سماكة الخط بالبكسل", "labelHTML": null, "placeholder": "أدخل سماكة الخط (1-10 بكسل)", "icon": "sicon-pencil-ruler", "value": 2, "required": false, "minimum": 1, "maximum": 10}, {"type": "static", "format": "title", "id": "static-title-spacing", "value": "إعدادات المسافات", "variant": "h6"}, {"id": "margin_top", "type": "number", "format": "integer", "inputType": "number", "label": "المسافة العلوية", "description": "المسافة من الأعلى بالبكسل", "labelHTML": null, "placeholder": "أدخل المسافة العلوية (0-100 بكسل)", "icon": "sicon-arrow-up", "value": 30, "required": false, "minimum": 0, "maximum": 100}, {"id": "margin_bottom", "type": "number", "format": "integer", "inputType": "number", "label": "المسافة السفلية", "description": "المسافة من الأسفل بالبكسل", "labelHTML": null, "placeholder": "أدخل المسافة السفلية (0-100 بكسل)", "icon": "sicon-arrow-down", "value": 30, "required": false, "minimum": 0, "maximum": 100}, {"type": "static", "format": "title", "id": "static-title-effects", "value": "إعدادات التأثيرات", "variant": "h6"}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تفعيل تأثير الإضاءة", "description": "إضافة تأثير إضاءة حول الخط", "id": "enable_glow", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تفعيل تأثير اللمعان", "description": "إضافة تأثير لمعان متحرك", "id": "enable_shine", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تفعيل الجزيئات المتحركة", "description": "إضافة جزيئات متحركة على الخط", "id": "enable_particles", "format": "switch", "required": false, "value": true, "selected": true}, {"id": "animation_speed", "type": "items", "format": "dropdown-list", "label": "سرعة الحركة", "description": "اختر سرعة الحركات والتأثيرات", "labelHTML": null, "icon": "sicon-speed", "selected": [{"label": "عادي", "value": "normal", "key": "animation-speed-normal"}], "options": [{"label": "بطيء جداً", "value": "very-slow", "key": "animation-speed-very-slow"}, {"label": "بطيء", "value": "slow", "key": "animation-speed-slow"}, {"label": "عادي", "value": "normal", "key": "animation-speed-normal"}, {"label": "سريع", "value": "fast", "key": "animation-speed-fast"}, {"label": "سريع جداً", "value": "very-fast", "key": "animation-speed-very-fast"}], "source": "Manual", "required": false, "multichoice": false}]}, {"key": "61e23991-53eb-4766-8169-0e8c5d6b69ff", "title": "معر<PERSON> افق 2", "icon": "sicon-store", "path": "home.secondGallary", "fields": [{"type": "static", "format": "title", "id": "static-title-content", "value": "إعدادات المحتوى", "variant": "h6"}, {"id": "title", "type": "string", "format": "text", "label": "عنوان المعرض", "description": "النص الذي سيظهر كعنوان للمعرض", "labelHTML": null, "placeholder": "أدخل عنوان المعرض هنا...", "icon": "sicon-format-text-alt", "value": "ادخل عنوان القسم", "multilanguage": true, "required": true, "minLength": "1", "maxLength": 50}, {"type": "static", "id": "static-line-1", "format": "line"}, {"type": "static", "format": "title", "id": "static-title-text-styling", "value": "إعدادات تنسيق النص", "variant": "h6"}, {"id": "title_font_size", "type": "items", "format": "dropdown-list", "label": "<PERSON><PERSON><PERSON> خط العنوان", "description": "اخ<PERSON>ر حجم خط عنوان المعرض", "labelHTML": null, "icon": "sicon-text-size", "selected": [{"label": "كبير", "value": "large", "key": "title-font-size-large"}], "options": [{"label": "صغير", "value": "small", "key": "title-font-size-small"}, {"label": "متوسط", "value": "medium", "key": "title-font-size-medium"}, {"label": "كبير", "value": "large", "key": "title-font-size-large"}, {"label": "كبير جداً", "value": "extra-large", "key": "title-font-size-extra-large"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "title_font_weight", "type": "items", "format": "dropdown-list", "label": "سُمك خط العنوان", "description": "اختر سُمك خط عنوان المعرض", "labelHTML": null, "icon": "sicon-bold", "selected": [{"label": "عريض", "value": "bold", "key": "title-font-weight-bold"}], "options": [{"label": "عادي", "value": "normal", "key": "title-font-weight-normal"}, {"label": "متوسط", "value": "medium", "key": "title-font-weight-medium"}, {"label": "عريض", "value": "bold", "key": "title-font-weight-bold"}, {"label": "عريض جداً", "value": "extra-bold", "key": "title-font-weight-extra-bold"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "title_text_alignment", "type": "items", "format": "dropdown-list", "label": "محاذاة العنوان", "description": "اختر محاذاة عنوان المعرض", "labelHTML": null, "icon": "sicon-text-align-center", "selected": [{"label": "وسط", "value": "center", "key": "title-text-alignment-center"}], "options": [{"label": "يمين", "value": "right", "key": "title-text-alignment-right"}, {"label": "وسط", "value": "center", "key": "title-text-alignment-center"}, {"label": "يسار", "value": "left", "key": "title-text-alignment-left"}], "source": "Manual", "required": false, "multichoice": false}, {"type": "static", "id": "static-line-2", "format": "line"}, {"type": "static", "format": "title", "id": "static-title-colors", "value": "إعدادات الألوان", "variant": "h6"}, {"type": "string", "icon": "sicon-palette", "label": "لون نص العنوان", "id": "title_text_color", "value": "#00ff88", "required": false, "format": "color", "description": "اختر لون نص عنوان المعرض"}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تفعيل خلفية ملونة", "description": "تفعيل أو إلغاء خلفية المعرض", "id": "enable_background", "format": "switch", "required": false, "value": false, "selected": false}, {"type": "string", "icon": "sicon-palette", "label": "لون الخلفية", "id": "background_color", "value": "#0a0a0a", "required": false, "format": "color", "description": "اختر لون خلفية المعرض"}, {"type": "string", "icon": "sicon-palette", "label": "لون التأثير عند التمرير", "id": "hover_overlay_color", "value": "#00ff88", "required": false, "format": "color", "description": "اختر لون التأثير الذي يظهر عند التمرير على الصور"}, {"type": "string", "icon": "sicon-palette", "label": "لون الحدود المضيئة", "id": "glow_border_color", "value": "#00d4ff", "required": false, "format": "color", "description": "اختر لون الحدود المضيئة للصور"}, {"id": "icon", "type": "collection", "format": "collection", "label": "icon", "description": null, "labelHTML": null, "item_label": null, "icon": "sicon-list-add", "fields": [{"id": "icon.image", "type": "string", "format": "image", "label": "صورة", "description": null, "labelHTML": null, "placeholder": "e.g. https://hostname.com/image.png", "icon": "sicon-image", "value": null, "required": false}, {"id": "icon.title", "type": "string", "format": "text", "label": "عنوان", "description": null, "labelHTML": null, "placeholder": "Placeholder text ..", "icon": "sicon-format-text-alt", "value": null, "multilanguage": false, "required": false, "minLength": "1", "maxLength": "50"}, {"id": "icon.url", "type": "items", "format": "variable-list", "label": "الرابط", "description": "رابط الصورة - سيتم التوجه إليه عند النقر على الصورة", "labelHTML": null, "placeholder": null, "icon": "sicon-link", "value": null, "required": false, "searchable": true, "source": "custom", "sources": [{"label": "منتج", "key": "products", "value": "products"}, {"label": "تصنيف", "key": "categories", "value": "categories"}, {"label": "ماركة تجارية", "key": "brands", "value": "brands"}, {"label": "صفحة تعريفية", "key": "pages", "value": "pages"}, {"label": "مقالة", "key": "blog_articles", "value": "blog_articles"}, {"label": "تصنيف ض<PERSON>ن المدونة", "key": "blog_categories", "value": "blog_categories"}, {"label": "التخفيضات", "key": "offers_link", "value": "offers_link"}, {"label": "الماركات التجارية", "key": "brands_link", "value": "brands_link"}, {"label": "المدونة", "key": "blog_link", "value": "blog_link"}, {"label": "را<PERSON><PERSON> خارجي", "key": "custom", "value": "custom"}]}], "value": [], "minLength": 1, "maxLength": "20"}, {"type": "static", "id": "static-line-3", "format": "line"}, {"type": "static", "format": "title", "id": "static-title-animation", "value": "إعدادات الحركة", "variant": "h6"}, {"id": "animation_type", "type": "items", "format": "dropdown-list", "label": "نوع حركة العنوان", "description": "اختر نوع حركة دخول عنوان المعرض", "labelHTML": null, "icon": "sicon-refresh", "selected": [{"label": "تلاشي (FadeIn)", "value": "fadeIn", "key": "title-animation-type-fadeIn"}], "options": [{"label": "تلاشي (FadeIn)", "value": "fadeIn", "key": "title-animation-type-fadeIn"}, {"label": "انزلاق للأعلى (SlideUp)", "value": "slideUp", "key": "title-animation-type-slideUp"}, {"label": "تكبير (ScaleIn)", "value": "scaleIn", "key": "title-animation-type-scaleIn"}, {"label": "ارتداد (BounceIn)", "value": "bounceIn", "key": "title-animation-type-bounceIn"}, {"label": "توسع التتبع (Tracking Expand)", "value": "tracking-expand", "key": "title-animation-type-tracking-expand"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "gallery_animation_type", "type": "items", "format": "dropdown-list", "label": "نوع حركة عناصر المعرض", "description": "اختر نوع حركة دخول عناصر المعرض", "labelHTML": null, "icon": "sicon-refresh", "selected": [{"label": "انزلاق للأعلى (SlideUp)", "value": "slideUp", "key": "gallery-animation-type-slideUp"}], "options": [{"label": "تلاشي (FadeIn)", "value": "fadeIn", "key": "gallery-animation-type-fadeIn"}, {"label": "انزلاق للأعلى (SlideUp)", "value": "slideUp", "key": "gallery-animation-type-slideUp"}, {"label": "تكبير (ScaleIn)", "value": "scaleIn", "key": "gallery-animation-type-scaleIn"}, {"label": "ارتداد (BounceIn)", "value": "bounceIn", "key": "gallery-animation-type-bounceIn"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "animation_duration", "type": "items", "format": "dropdown-list", "label": "مدة الحركة", "description": "اختر مدة تشغيل الحركة", "labelHTML": null, "icon": "sicon-clock", "selected": [{"label": "متوسط (1 ثانية)", "value": "medium", "key": "animation-duration-medium"}], "options": [{"label": "سريع (0.5 ثانية)", "value": "fast", "key": "animation-duration-fast"}, {"label": "متوسط (1 ثانية)", "value": "medium", "key": "animation-duration-medium"}, {"label": "بطيء (1.5 ثانية)", "value": "slow", "key": "animation-duration-slow"}, {"label": "بطيء جداً (2 ثانية)", "value": "extra-slow", "key": "animation-duration-extra-slow"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "animation_delay", "type": "items", "format": "dropdown-list", "label": "تأخير الحركة", "description": "اختر مدة تأخير بدء الحركة", "labelHTML": null, "icon": "sicon-clock", "selected": [{"label": "قصير (0.2 ثانية)", "value": "short", "key": "animation-delay-short"}], "options": [{"label": "بدون تأخير", "value": "none", "key": "animation-delay-none"}, {"label": "قصير (0.2 ثانية)", "value": "short", "key": "animation-delay-short"}, {"label": "متوسط (0.5 ثانية)", "value": "medium", "key": "animation-delay-medium"}, {"label": "طويل (1 ثانية)", "value": "long", "key": "animation-delay-long"}], "source": "Manual", "required": false, "multichoice": false}, {"type": "static", "id": "static-line-4", "format": "line"}, {"type": "static", "format": "title", "id": "static-title-layout", "value": "إعدادات التخطيط", "variant": "h6"}, {"id": "items_per_row", "type": "items", "format": "dropdown-list", "label": "ع<PERSON><PERSON> العناصر في الصف", "description": "ا<PERSON><PERSON>ر عدد العناصر المعروضة في كل صف", "labelHTML": null, "icon": "sicon-grid", "selected": [{"label": "3 عناصر", "value": "3", "key": "items-per-row-3"}], "options": [{"label": "2 عناصر", "value": "2", "key": "items-per-row-2"}, {"label": "3 عناصر", "value": "3", "key": "items-per-row-3"}, {"label": "4 عناصر", "value": "4", "key": "items-per-row-4"}, {"label": "5 عناصر", "value": "5", "key": "items-per-row-5"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "top_margin", "type": "number", "format": "number", "label": "المسافة العلوية", "description": "المسافة العلوية للمعرض بالبكسل", "labelHTML": null, "icon": "sicon-arrow-up", "value": 80, "required": false, "min": 0, "max": 200, "step": 10}, {"id": "bottom_margin", "type": "number", "format": "number", "label": "المسافة السفلية", "description": "المسافة السفلية للمعرض بالبكسل", "labelHTML": null, "icon": "sicon-arrow-down", "value": 80, "required": false, "min": 0, "max": 200, "step": 10}, {"type": "boolean", "icon": "sicon-expand", "label": "عر<PERSON> كامل", "description": "جعل المعرض يم<PERSON>د عبر العرض الكامل للشاشة", "id": "full_width", "format": "switch", "required": false, "value": false, "selected": false}, {"type": "static", "id": "static-line-5", "format": "line"}, {"type": "static", "format": "title", "id": "static-title-visual-effects", "value": "التأثيرات البصرية", "variant": "h6"}, {"type": "boolean", "icon": "sicon-magic-wand", "label": "تفعيل تأثيرات التمرير", "description": "تفعيل التأثيرات البصرية عند التمرير على الصور", "id": "enable_hover_effects", "format": "switch", "required": false, "value": true, "selected": true}, {"id": "hover_animation_type", "type": "items", "format": "dropdown-list", "label": "نوع حركة التمرير", "description": "اختر نوع الحركة عند التمرير على الصور", "labelHTML": null, "icon": "sicon-refresh", "selected": [{"label": "تكبير (Scale)", "value": "scale", "key": "hover-animation-type-scale"}], "options": [{"label": "تكبير (Scale)", "value": "scale", "key": "hover-animation-type-scale"}, {"label": "دوران (Rotate)", "value": "rotate", "key": "hover-animation-type-rotate"}, {"label": "انزلاق (Slide)", "value": "slide", "key": "hover-animation-type-slide"}, {"label": "نبضة (Pulse)", "value": "pulse", "key": "hover-animation-type-pulse"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "image_border_radius", "type": "number", "format": "number", "label": "انحناء زوايا الصور", "description": "مقدار انحناء زوايا الصور بالبكسل", "labelHTML": null, "icon": "sicon-border-radius", "value": 15, "required": false, "min": 0, "max": 50, "step": 5}, {"type": "boolean", "icon": "sicon-shadow", "label": "تفعيل الظلال", "description": "إضافة ظلال للصور", "id": "enable_image_shadows", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "boolean", "icon": "sicon-sparkles", "label": "تفعيل التأثيرات المضيئة", "description": "إضافة تأثيرات مضيئة حول الصور", "id": "enable_glow_effects", "format": "switch", "required": false, "value": true, "selected": true}]}, {"key": "9d02de6f-5797-4820-a39c-71b85d96432d", "title": "عنوان مميز مزخرف", "icon": "sicon-italic", "path": "home.specialTitle", "fields": [{"type": "static", "format": "title", "id": "static-title-content", "value": "إعدادات المحتوى", "variant": "h6"}, {"id": "title", "type": "string", "format": "text", "label": "عنوان مميز", "description": "النص الذي سيظهر في العنوان المزخرف", "labelHTML": null, "placeholder": "أدخل العنوان المميز هنا...", "icon": "sicon-format-text-alt", "value": null, "multilanguage": true, "required": true, "minLength": "1", "maxLength": 50}, {"type": "static", "id": "static-line-1", "format": "line"}, {"type": "static", "format": "title", "id": "static-title-styling", "value": "إعدادات التصميم", "variant": "h6"}, {"id": "font_size", "type": "items", "format": "dropdown-list", "label": "حج<PERSON> الخط", "description": "اخ<PERSON>ر حجم خط العنوان", "labelHTML": null, "icon": "sicon-format-size", "selected": [{"label": "كبير", "value": "large", "key": "font-size-large"}], "options": [{"label": "صغير", "value": "small", "key": "font-size-small"}, {"label": "متوسط", "value": "medium", "key": "font-size-medium"}, {"label": "كبير", "value": "large", "key": "font-size-large"}, {"label": "كبير جداً", "value": "extra-large", "key": "font-size-extra-large"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "font_weight", "type": "items", "format": "dropdown-list", "label": "سماكة الخط", "description": "اختر سماكة خط العنوان", "labelHTML": null, "icon": "sicon-format-bold", "selected": [{"label": "عريض", "value": "bold", "key": "font-weight-bold"}], "options": [{"label": "عادي", "value": "normal", "key": "font-weight-normal"}, {"label": "متوسط", "value": "medium", "key": "font-weight-medium"}, {"label": "عريض", "value": "bold", "key": "font-weight-bold"}, {"label": "عريض جداً", "value": "extra-bold", "key": "font-weight-extra-bold"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "text_alignment", "type": "items", "format": "dropdown-list", "label": "محاذاة النص", "description": "اختر محاذاة العنوان", "labelHTML": null, "icon": "sicon-format-align-center", "selected": [{"label": "وسط", "value": "center", "key": "text-align-center"}], "options": [{"label": "يمين", "value": "right", "key": "text-align-right"}, {"label": "وسط", "value": "center", "key": "text-align-center"}, {"label": "يسار", "value": "left", "key": "text-align-left"}], "source": "Manual", "required": false, "multichoice": false}, {"type": "static", "id": "static-line-2", "format": "line"}, {"type": "static", "format": "title", "id": "static-title-colors", "value": "إعدادات الألوان", "variant": "h6"}, {"type": "string", "icon": "sicon-palette", "label": "لون النص الأساسي", "id": "text_color", "value": "#1DE9B6", "required": false, "format": "color", "description": "اختر لون نص العنوان"}, {"type": "string", "icon": "sicon-palette", "label": "لون الظل", "id": "text_shadow_color", "value": "#1DE9B6", "required": false, "format": "color", "description": "اختر لون ظل النص"}, {"type": "string", "icon": "sicon-palette", "label": "لون الزخرفة", "id": "decoration_color", "value": "#1DE9B6", "required": false, "format": "color", "description": "اختر لون الخطوط والأيقونات الزخرفية"}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تفعيل خلفية ملونة", "description": "تفعيل أو إلغاء خلفية المكون", "id": "enable_background", "format": "switch", "required": false, "value": false, "selected": false}, {"type": "string", "icon": "sicon-palette", "label": "لون الخلفية", "id": "background_color", "value": "#000000", "required": false, "format": "color", "description": "اختر لون خلفية المكون"}, {"type": "static", "id": "static-line-3", "format": "line"}, {"type": "static", "format": "title", "id": "static-title-animation", "value": "إعدادات الحركة", "variant": "h6"}, {"id": "animation_type", "type": "items", "format": "dropdown-list", "label": "نوع الحركة", "description": "اختر نوع حركة دخول العنوان", "labelHTML": null, "icon": "sicon-refresh", "selected": [{"label": "توسع التتبع (Tracking Expand)", "value": "tracking-expand", "key": "animation-type-tracking-expand"}], "options": [{"label": "توسع التتبع (Tracking Expand)", "value": "tracking-expand", "key": "animation-type-tracking-expand"}, {"label": "تلاشي (FadeIn)", "value": "fadeIn", "key": "animation-type-fadeIn"}, {"label": "انزلاق للأعلى (SlideUp)", "value": "slideUp", "key": "animation-type-slideUp"}, {"label": "تكبير (ScaleIn)", "value": "scaleIn", "key": "animation-type-scaleIn"}, {"label": "ارتداد (BounceIn)", "value": "bounceIn", "key": "animation-type-bounceIn"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "animation_duration", "type": "items", "format": "dropdown-list", "label": "مدة الحركة", "description": "اختر مدة تشغيل الحركة", "labelHTML": null, "icon": "sicon-clock", "selected": [{"label": "متوسط (1 ثانية)", "value": "medium", "key": "animation-duration-medium"}], "options": [{"label": "سريع (0.5 ثانية)", "value": "fast", "key": "animation-duration-fast"}, {"label": "متوسط (1 ثانية)", "value": "medium", "key": "animation-duration-medium"}, {"label": "بطيء (1.5 ثانية)", "value": "slow", "key": "animation-duration-slow"}, {"label": "بطيء جداً (2 ثانية)", "value": "extra-slow", "key": "animation-duration-extra-slow"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "animation_delay", "type": "items", "format": "dropdown-list", "label": "تأخير الحركة", "description": "اختر مدة تأخير بدء الحركة", "labelHTML": null, "icon": "sicon-clock", "selected": [{"label": "بدون تأخير", "value": "none", "key": "animation-delay-none"}], "options": [{"label": "بدون تأخير", "value": "none", "key": "animation-delay-none"}, {"label": "قصير (0.2 ثانية)", "value": "short", "key": "animation-delay-short"}, {"label": "متوسط (0.5 ثانية)", "value": "medium", "key": "animation-delay-medium"}, {"label": "طويل (1 ثانية)", "value": "long", "key": "animation-delay-long"}], "source": "Manual", "required": false, "multichoice": false}, {"type": "static", "id": "static-line-4", "format": "line"}, {"type": "static", "format": "title", "id": "static-title-layout", "value": "إعدادات التخطيط", "variant": "h6"}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "عرض بعرض الشاشة الكامل", "description": "جعل المكون يمتد عبر عرض الشاشة الكامل خارج الحاوية", "id": "full_width", "format": "switch", "required": false, "value": false, "selected": false}, {"id": "top_margin", "type": "number", "format": "integer", "inputType": "number", "label": "المسافة العلوية", "description": "المسافة العلوية بالبكسل", "labelHTML": null, "placeholder": "أدخل المسافة العلوية (0-100 بكسل)", "icon": "sicon-arrow-up", "value": 60, "required": false, "minimum": 0, "maximum": 100}, {"id": "bottom_margin", "type": "number", "format": "integer", "inputType": "number", "label": "المسافة السفلية", "description": "المسافة السفلية بالبكسل", "labelHTML": null, "placeholder": "أدخل المسافة السفلية (0-100 بكسل)", "icon": "sicon-arrow-down", "value": 60, "required": false, "minimum": 0, "maximum": 100}, {"type": "static", "id": "static-line-5", "format": "line"}, {"type": "static", "format": "title", "id": "static-title-effects", "value": "التأثيرات البصرية", "variant": "h6"}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تفعيل ظل النص", "description": "إضافة ظل مضيء حول النص", "id": "enable_text_shadow", "format": "switch", "required": false, "value": true, "selected": true}, {"id": "text_shadow_intensity", "type": "number", "format": "integer", "inputType": "number", "label": "قوة ظل النص", "description": "قوة ظل النص (0-50)", "labelHTML": null, "placeholder": "أدخل قوة الظل (0-50)", "icon": "sicon-shadow", "value": 20, "required": false, "minimum": 0, "maximum": 50}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تفعيل الجسيمات المتحركة", "description": "إضافة جسيمات متحركة في الخلفية", "id": "enable_particles", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تفعيل الزخرفة الدائرية", "description": "إضافة خطوط زخرفية دائرية متحركة", "id": "enable_circuit_decoration", "format": "switch", "required": false, "value": true, "selected": true}, {"id": "decoration_style", "type": "items", "format": "dropdown-list", "label": "نمط الزخرفة", "description": "اختر نمط الزخرفة السفلية", "labelHTML": null, "icon": "sicon-star", "selected": [{"label": "خطوط مع أيقونة", "value": "lines-with-icon", "key": "decoration-style-lines-with-icon"}], "options": [{"label": "خطوط مع أيقونة", "value": "lines-with-icon", "key": "decoration-style-lines-with-icon"}, {"label": "خطوط فقط", "value": "lines-only", "key": "decoration-style-lines-only"}, {"label": "أيقونة فقط", "value": "icon-only", "key": "decoration-style-icon-only"}, {"label": "بدون زخرفة", "value": "none", "key": "decoration-style-none"}], "source": "Manual", "required": false, "multichoice": false}]}, {"key": "c898fd13-de35-43ad-bef0-03a7f2c0847f", "title": "احصائيات المتجر", "icon": "sicon-checklist", "path": "home.storeStats", "fields": [{"id": "description", "type": "static", "format": "description", "label": "وصف العنصر", "value": "قم باختيار ايقونة للاحصائية ثم عنوان ثم رقم للاحصائية", "variant": "h1", "icon": "sicon-format-size"}, {"id": "title", "type": "string", "format": "text", "label": "عنوان المكون", "description": null, "labelHTML": null, "placeholder": "اضف عنوان مثل \"احصائيات المتجر \"", "icon": "sicon-format-text-alt", "value": null, "multilanguage": false, "required": false, "minLength": "5", "maxLength": 30}, {"id": "stats", "type": "collection", "format": "collection", "label": "الاحصائية", "description": null, "labelHTML": null, "item_label": null, "icon": "sicon-list-add", "fields": [{"id": "stats.icon", "type": "string", "format": "icon", "label": "ايقونة", "description": null, "labelHTML": null, "icon": "sicon-user-circle", "value": "sicon-store", "required": false}, {"id": "stats.title", "type": "string", "format": "text", "label": "عنوان الاحصائية", "description": null, "labelHTML": null, "placeholder": "Placeholder text ..", "icon": "sicon-format-text-alt", "value": null, "multilanguage": false, "required": false, "minLength": 6, "maxLength": 30}, {"id": "stats.number", "type": "number", "format": "integer", "inputType": "number", "label": "رقم الاحصائية", "description": null, "labelHTML": null, "placeholder": "Input an integer number between 5 and 50.", "icon": "sicon-hashtag", "value": null, "required": false, "minimum": 0, "maximum": "10000"}], "value": [], "minLength": 1, "maxLength": "4"}]}, {"key": "88484f0f-d5bc-4dd9-b732-08318ca0d61f", "title": "بنرات متحركة جديدة", "icon": "sicon-store", "path": "home.newBanners", "fields": [{"type": "static", "format": "title", "id": "static-title-content", "value": "إعدادات المحتوى", "variant": "h6"}, {"id": "banner", "type": "collection", "format": "collection", "label": "البنر", "description": "إضافة وإدارة البنرات المتحركة", "labelHTML": null, "item_label": "بنر", "icon": "sicon-list-add", "required": true, "minLength": 1, "maxLength": 10, "fields": [{"id": "banner.img", "type": "string", "format": "image", "label": "صورة البنر", "description": "المقاس المناسب: 1920×600 بكسل", "labelHTML": null, "placeholder": "e.g. https://hostname.com/image.png", "icon": "sicon-image", "value": null, "required": true, "settings": {"width": 1920, "height": 600}}, {"id": "banner.title", "type": "string", "format": "text", "label": "عنوان البنر", "description": null, "labelHTML": null, "placeholder": "Placeholder text ..", "icon": "sicon-format-text-alt", "value": null, "multilanguage": false, "required": false, "minLength": "1", "maxLength": "50"}, {"id": "banner.subtitle", "type": "string", "format": "text", "label": "وصف البنر", "description": null, "labelHTML": null, "placeholder": "Placeholder text ..", "icon": "sicon-format-text-alt", "value": null, "multilanguage": false, "required": false, "minLength": "1", "maxLength": "50"}, {"type": "items", "icon": "sicon-link", "label": "الرابط", "id": "banner.url", "value": null, "description": "", "required": false, "format": "variable-list", "searchable": true, "source": "custom", "sources": [{"label": "منتج", "key": "products", "value": "products"}, {"label": "تصنيف", "key": "categories", "value": "categories"}, {"label": "ماركة تجارية", "key": "brands", "value": "brands"}, {"label": "صفحة تعريفية", "key": "pages", "value": "pages"}, {"label": "مقالة", "key": "blog_articles", "value": "blog_articles"}, {"label": "تصنيف ض<PERSON>ن المدونة", "key": "blog_categories", "value": "blog_categories"}, {"label": "التخفيضات", "key": "offers_link", "value": "offers_link"}, {"label": "الماركات التجارية", "key": "brands_link", "value": "brands_link"}, {"label": "المدونة", "key": "blog_link", "value": "blog_link"}, {"label": "را<PERSON><PERSON> خارجي", "key": "custom", "value": "custom"}]}], "value": []}, {"type": "static", "id": "static-line-1", "format": "line"}, {"type": "static", "format": "title", "id": "static-title-text-styling", "value": "إعدادات النصوص", "variant": "h6"}, {"id": "title_font_size", "type": "items", "format": "dropdown-list", "label": "حج<PERSON> خط العنوان الرئيسي", "description": "ا<PERSON><PERSON><PERSON> حجم خط العنوان الرئيسي للبنرات", "labelHTML": null, "icon": "sicon-format-text-size", "selected": [{"label": "كبير", "value": "large", "key": "title-font-size-large"}], "options": [{"label": "صغير", "value": "small", "key": "title-font-size-small"}, {"label": "متوسط", "value": "medium", "key": "title-font-size-medium"}, {"label": "كبير", "value": "large", "key": "title-font-size-large"}, {"label": "كبير جداً", "value": "extra-large", "key": "title-font-size-extra-large"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "title_font_weight", "type": "items", "format": "dropdown-list", "label": "سُمك خط العنوان الرئيسي", "description": "اختر سُمك خط العنوان الرئيسي", "labelHTML": null, "icon": "sicon-format-bold", "selected": [{"label": "عريض", "value": "bold", "key": "title-font-weight-bold"}], "options": [{"label": "عادي", "value": "normal", "key": "title-font-weight-normal"}, {"label": "متوسط", "value": "medium", "key": "title-font-weight-medium"}, {"label": "عريض", "value": "bold", "key": "title-font-weight-bold"}, {"label": "عريض جداً", "value": "extra-bold", "key": "title-font-weight-extra-bold"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "subtitle_font_size", "type": "items", "format": "dropdown-list", "label": "حج<PERSON> <PERSON>ط العنوان الفرعي", "description": "ا<PERSON><PERSON><PERSON> حجم خط العنوان الفرعي للبنرات", "labelHTML": null, "icon": "sicon-format-text-size", "selected": [{"label": "متوسط", "value": "medium", "key": "subtitle-font-size-medium"}], "options": [{"label": "صغير", "value": "small", "key": "subtitle-font-size-small"}, {"label": "متوسط", "value": "medium", "key": "subtitle-font-size-medium"}, {"label": "كبير", "value": "large", "key": "subtitle-font-size-large"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "text_alignment", "type": "items", "format": "dropdown-list", "label": "محاذاة النصوص", "description": "اختر محاذاة النصوص في البنرات", "labelHTML": null, "icon": "sicon-format-align-center", "selected": [{"label": "يسار", "value": "left", "key": "text-alignment-left"}], "options": [{"label": "يمين", "value": "right", "key": "text-alignment-right"}, {"label": "وسط", "value": "center", "key": "text-alignment-center"}, {"label": "يسار", "value": "left", "key": "text-alignment-left"}], "source": "Manual", "required": false, "multichoice": false}, {"type": "static", "id": "static-line-2", "format": "line"}, {"type": "static", "format": "title", "id": "static-title-colors", "value": "إعدادات الألوان", "variant": "h6"}, {"type": "string", "icon": "sicon-format-fill", "label": "لون العنوان الرئيسي", "description": "اختر لون العنوان الرئيسي للبنرات", "id": "title_text_color", "format": "color", "inputType": "color", "required": false, "value": "#ffffff"}, {"type": "string", "icon": "sicon-format-fill", "label": "لون العنوان الفرعي", "description": "اختر لون العنوان الفرعي للبنرات", "id": "subtitle_text_color", "format": "color", "inputType": "color", "required": false, "value": "#e0e0e0"}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تفعيل خلفية مخصصة", "description": "تفعيل أو إلغاء الخلفية المخصصة للبنرات", "id": "enable_background", "format": "switch", "required": false, "value": false, "selected": false}, {"type": "string", "icon": "sicon-format-fill", "label": "لون الخلفية", "description": "اختر لون الخلفية (يظهر عند التفعيل)", "id": "background_color", "format": "color", "inputType": "color", "required": false, "value": "#0a0a0a"}, {"type": "string", "icon": "sicon-format-fill", "label": "لون تأثير التمرير", "description": "اختر لون التأثير عند التمرير على البنرات", "id": "hover_overlay_color", "format": "color", "inputType": "color", "required": false, "value": "#00ff88"}, {"type": "string", "icon": "sicon-format-fill", "label": "لون الحدود المضيئة", "description": "اختر لون الحدود المضيئة للبنرات", "id": "glow_border_color", "format": "color", "inputType": "color", "required": false, "value": "#00d4ff"}, {"type": "static", "id": "static-line-3", "format": "line"}, {"type": "static", "format": "title", "id": "static-title-animations", "value": "إعدادات الحركة", "variant": "h6"}, {"id": "banner_animation_type", "type": "items", "format": "dropdown-list", "label": "نوع حركة البنرات", "description": "اختر نوع حركة دخول البنرات", "labelHTML": null, "icon": "sicon-refresh", "selected": [{"label": "تلاشي (FadeIn)", "value": "fadeIn", "key": "banner-animation-type-fadeIn"}], "options": [{"label": "تلاشي (FadeIn)", "value": "fadeIn", "key": "banner-animation-type-fadeIn"}, {"label": "انزلاق للأعلى (SlideUp)", "value": "slideUp", "key": "banner-animation-type-slideUp"}, {"label": "تكبير (ScaleIn)", "value": "scaleIn", "key": "banner-animation-type-scaleIn"}, {"label": "ارتداد (BounceIn)", "value": "bounceIn", "key": "banner-animation-type-bounceIn"}, {"label": "انزلاق من اليسار (SlideLeft)", "value": "slideLeft", "key": "banner-animation-type-slideLeft"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "text_animation_type", "type": "items", "format": "dropdown-list", "label": "نوع حركة النصوص", "description": "اختر نوع حركة دخول النصوص", "labelHTML": null, "icon": "sicon-refresh", "selected": [{"label": "انزلاق للأعلى (SlideUp)", "value": "slideUp", "key": "text-animation-type-slideUp"}], "options": [{"label": "تلاشي (FadeIn)", "value": "fadeIn", "key": "text-animation-type-fadeIn"}, {"label": "انزلاق للأعلى (SlideUp)", "value": "slideUp", "key": "text-animation-type-slideUp"}, {"label": "تكبير (ScaleIn)", "value": "scaleIn", "key": "text-animation-type-scaleIn"}, {"label": "توسع الحروف (Tracking Expand)", "value": "trackingExpand", "key": "text-animation-type-trackingExpand"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "animation_duration", "type": "items", "format": "dropdown-list", "label": "مدة الحركة", "description": "اختر مدة تشغيل الحركة", "labelHTML": null, "icon": "sicon-clock", "selected": [{"label": "متوسط (1 ثانية)", "value": "medium", "key": "animation-duration-medium"}], "options": [{"label": "سريع (0.5 ثانية)", "value": "fast", "key": "animation-duration-fast"}, {"label": "متوسط (1 ثانية)", "value": "medium", "key": "animation-duration-medium"}, {"label": "بطيء (1.5 ثانية)", "value": "slow", "key": "animation-duration-slow"}, {"label": "بطيء جداً (2 ثانية)", "value": "extra-slow", "key": "animation-duration-extra-slow"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "animation_delay", "type": "items", "format": "dropdown-list", "label": "تأخير الحركة", "description": "اختر تأخير بدء الحركة", "labelHTML": null, "icon": "sicon-clock", "selected": [{"label": "قصير (0.2 ثانية)", "value": "short", "key": "animation-delay-short"}], "options": [{"label": "بدون تأخير", "value": "none", "key": "animation-delay-none"}, {"label": "قصير (0.2 ثانية)", "value": "short", "key": "animation-delay-short"}, {"label": "متوسط (0.5 ثانية)", "value": "medium", "key": "animation-delay-medium"}, {"label": "طويل (1 ثانية)", "value": "long", "key": "animation-delay-long"}], "source": "Manual", "required": false, "multichoice": false}, {"type": "static", "id": "static-line-4", "format": "line"}, {"type": "static", "format": "title", "id": "static-title-layout", "value": "إعدادات التخطيط", "variant": "h6"}, {"id": "banner_height", "type": "number", "format": "number", "label": "ارتفاع البنرات", "description": "ارتفاع البنرات بالبكسل", "labelHTML": null, "icon": "sicon-arrow-up-down", "value": 500, "required": false, "min": 300, "max": 800, "step": 50}, {"id": "top_margin", "type": "number", "format": "number", "label": "المسافة العلوية", "description": "المسافة العلوية للبنرات بالبكسل", "labelHTML": null, "icon": "sicon-arrow-up", "value": 0, "required": false, "min": 0, "max": 200, "step": 10}, {"id": "bottom_margin", "type": "number", "format": "number", "label": "المسافة السفلية", "description": "المسافة السفلية للبنرات بالبكسل", "labelHTML": null, "icon": "sicon-arrow-down", "value": 40, "required": false, "min": 0, "max": 200, "step": 10}, {"type": "boolean", "icon": "sicon-expand", "label": "عر<PERSON> كامل", "description": "جعل البنرات تمتد عبر العرض الكامل للشاشة", "id": "full_width", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "static", "id": "static-line-5", "format": "line"}, {"type": "static", "format": "title", "id": "static-title-visual-effects", "value": "التأثيرات البصرية", "variant": "h6"}, {"type": "boolean", "icon": "sicon-magic-wand", "label": "تفعيل تأثيرات التمرير", "description": "تفعيل التأثيرات البصرية عند التمرير على البنرات", "id": "enable_hover_effects", "format": "switch", "required": false, "value": true, "selected": true}, {"id": "hover_animation_type", "type": "items", "format": "dropdown-list", "label": "نوع حركة التمرير", "description": "اختر نوع الحركة عند التمرير على البنرات", "labelHTML": null, "icon": "sicon-refresh", "selected": [{"label": "تكبير (Scale)", "value": "scale", "key": "hover-animation-type-scale"}], "options": [{"label": "تكبير (Scale)", "value": "scale", "key": "hover-animation-type-scale"}, {"label": "انزلاق (Slide)", "value": "slide", "key": "hover-animation-type-slide"}, {"label": "نبضة (Pulse)", "value": "pulse", "key": "hover-animation-type-pulse"}, {"label": "إضاءة (Glow)", "value": "glow", "key": "hover-animation-type-glow"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "image_border_radius", "type": "number", "format": "number", "label": "انحناء زوايا الصور", "description": "مقدار انحناء زوايا الصور بالبكسل", "labelHTML": null, "icon": "sicon-border-radius", "value": 0, "required": false, "min": 0, "max": 50, "step": 5}, {"type": "boolean", "icon": "sicon-shadow", "label": "تفعيل الظلال", "description": "إضافة ظلال للبنرات", "id": "enable_image_shadows", "format": "switch", "required": false, "value": false, "selected": false}, {"type": "boolean", "icon": "sicon-sparkles", "label": "تفعيل التأثيرات المضيئة", "description": "إضافة تأثيرات مضيئة حول البنرات", "id": "enable_glow_effects", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "boolean", "icon": "sicon-sparkles", "label": "تفعيل الجسيمات المتحركة", "description": "إضافة جسيمات متحركة في الخلفية", "id": "enable_particles", "format": "switch", "required": false, "value": true, "selected": true}]}, {"key": "f6d3ae0b-ab26-4d18-9b70-bbdc0799e63d", "title": "معرض افق المميز", "icon": "sicon-store", "path": "home.specialGallary", "fields": [{"id": "banners", "type": "collection", "format": "collection", "label": "قائمة الصور", "description": null, "labelHTML": null, "item_label": null, "icon": "sicon-list-add", "fields": [{"id": "banners.image", "type": "string", "format": "image", "label": "صورة البنر", "description": "اختر صورة", "labelHTML": null, "placeholder": "e.g. https://hostname.com/image.png", "icon": "sicon-image", "value": null, "required": false}, {"id": "banners.title", "type": "string", "format": "text", "label": "عنوان البنر", "description": "قم باضافة عنوان", "labelHTML": null, "placeholder": "Placeholder text ..", "icon": "sicon-format-text-alt", "value": null, "multilanguage": false, "required": false, "minLength": "4", "maxLength": 30}, {"id": "banners.url", "type": "items", "format": "variable-list", "label": "رابط", "value": [], "description": null, "required": false, "icon": "sicon-link", "source": "custom", "sources": [{"label": "منتج", "key": "products", "value": "products"}, {"label": "تصنيف", "key": "categories", "value": "categories"}, {"label": "ماركة تجارية", "key": "brands", "value": "brands"}, {"label": "صفحة تعريفية", "key": "pages", "value": "pages"}, {"label": "مقالة", "key": "blog_articles", "value": "blog_articles"}, {"label": "تصنيف ض<PERSON>ن المدونة", "key": "blog_categories", "value": "blog_categories"}, {"label": "التخفيضات", "key": "offers_link", "value": "offers_link"}, {"label": "الماركات التجارية", "key": "brands_link", "value": "brands_link"}, {"label": "المدونة", "key": "blog_link", "value": "blog_link"}, {"label": "را<PERSON><PERSON> خارجي", "key": "custom", "value": "custom"}], "multichoice": false, "searchable": true}, {"id": "banners.description", "type": "string", "format": "textarea", "label": "وصف البنر", "description": "قم باضافة نص يعبر عن البنر او الصورة", "labelHTML": null, "placeholder": "Your text here", "icon": "sicon-typography", "value": null, "multilanguage": false, "required": false, "minLength": "5", "maxLength": 255}, {"id": "banners.buttonTitle", "type": "string", "format": "text", "label": "نص ال زر", "description": null, "labelHTML": null, "placeholder": "اضف نص مثلا \"اكتشف الان\"", "icon": "sicon-format-text-alt", "value": null, "multilanguage": false, "required": false, "minLength": 6, "maxLength": 30}], "value": [], "minLength": 1, "maxLength": "5"}]}, {"key": "0b0cee7b-64db-4987-9291-6c454e8b6b8e", "title": "بنر فيديو", "icon": "sicon-lang", "path": "home.videoBanner", "fields": [{"type": "static", "format": "description", "id": "static-desc", "value": "<div style='padding-top:10px;margin-bottom:30px;border:1px solid #ebebeb;border-radius:5px;text-align:center;overflow:hidden'><h6 style='margin-bottom:10px'>معاينة العنصر</h6><p style='color:#666;font-size:14px;'>بنر متعدد الوسائط (فيديو أو صورة GIF) مع عنوان رئيسي وفرعي</p></div>"}, {"type": "string", "icon": "sicon-video", "label": "رابط الفيديو أو صورة GIF", "id": "video_url", "value": null, "required": true, "format": "text", "description": "أدخل رابط الفيديو (YouTube, Vimeo, رابط مباشر للفيديو) أو رابط صورة GIF متحركة", "placeholder": "https://www.youtube.com/watch?v=VIDEO_ID أو https://example.com/image.gif", "minLength": 10, "maxLength": 500}, {"type": "string", "icon": "sicon-format-text-alt", "label": "العنوان الرئيسي", "multilanguage": true, "id": "title", "value": null, "required": false, "format": "text", "description": "العنوان الذي سيظهر على الفيديو بخط كبير وبارز", "placeholder": "أدخل العنوان الرئيسي هنا...", "minLength": 0, "maxLength": 100}, {"type": "string", "icon": "sicon-typography", "label": "العنوان الفرعي", "multilanguage": true, "id": "subtitle", "value": null, "description": "النص التوضيحي الذي سيظهر تحت العنوان الرئيسي", "format": "textarea", "required": false, "placeholder": "أدخل العنوان الفرعي أو الوصف هنا...", "minLength": 0, "maxLength": 255}, {"type": "string", "icon": "sicon-format-text-alt", "label": "نص الزر", "multilanguage": true, "id": "button_text", "value": null, "required": false, "format": "text", "description": "النص الذي سيظهر على الزر (اختياري)", "placeholder": "اكتش<PERSON> المزيد", "minLength": 0, "maxLength": 50}, {"type": "items", "icon": "sicon-link", "label": "رابط الزر", "id": "button_url", "value": [], "description": "الرابط الذي سيتم الانتقال إليه عند النقر على الزر", "required": false, "format": "variable-list", "searchable": true, "source": "custom", "sources": [{"label": "منتج", "key": "products", "value": "products"}, {"label": "تصنيف", "key": "categories", "value": "categories"}, {"label": "ماركة تجارية", "key": "brands", "value": "brands"}, {"label": "صفحة تعريفية", "key": "pages", "value": "pages"}, {"label": "مقالة", "key": "blog_articles", "value": "blog_articles"}, {"label": "تصنيف ض<PERSON>ن المدونة", "key": "blog_categories", "value": "blog_categories"}, {"label": "التخفيضات", "key": "offers_link", "value": "offers_link"}, {"label": "الماركات التجارية", "key": "brands_link", "value": "brands_link"}, {"label": "المدونة", "key": "blog_link", "value": "blog_link"}, {"label": "را<PERSON><PERSON> خارجي", "key": "custom", "value": "custom"}]}]}, {"key": "83f062cc-db52-4c98-881e-b7076e47c746", "title": "الاسئلة الشائعة", "icon": "sicon-dollar-coin-stack", "path": "home.commonQuestions", "fields": [{"type": "static", "format": "description", "id": "static-desc", "value": "<div ><h6 >معاينة العنصر</h6><p>كومبوننت الأسئلة الشائعة يعرض مجموعة من الأسئلة والأجوبة بتصميم accordion تفاعلي</p></div>"}, {"type": "string", "icon": "sicon-format-text-alt", "label": "العنوان الرئيسي", "multilanguage": true, "id": "title", "value": {"ar": "الأسئلة الشائعة", "en": "Frequently Asked Questions"}, "required": false, "format": "text", "description": "العنوان الذي سيظهر أعلى قسم الأسئلة الشائعة", "placeholder": "أدخل العنوان هنا...", "minLength": 0, "maxLength": 100}, {"type": "string", "icon": "sicon-typography", "label": "نص توضيحي", "multilanguage": true, "id": "description", "value": {"ar": "إجابات على الأسئلة الأكثر شيوعاً", "en": "Answers to the most frequently asked questions"}, "description": "نص توضيحي يظهر تحت العنوان الرئيسي", "format": "textarea", "required": false, "placeholder": "يمكنك إضافة وصف توضيحي هنا...", "minLength": 0, "maxLength": 255}, {"id": "questions", "type": "collection", "format": "collection", "required": true, "minLength": 1, "maxLength": 20, "label": "قائمة الأسئلة والأجوبة", "item_label": "سؤال", "value": [{"questions.question": "ما هي طرق الدفع المتاحة؟", "questions.answer": "نقبل جميع طرق الدفع الإلكترونية المتاحة في المملكة العربية السعودية مثل مدى، فيزا، ماستركارد، والدفع عند الاستلام."}, {"questions.question": "كم تستغرق عملية التوصيل؟", "questions.answer": "عادة ما تستغرق عملية التوصيل من 2-5 أيام عمل حسب المنطقة الجغرافية."}, {"questions.question": "هل يمكنني إرجاع المنتج؟", "questions.answer": "نعم، يمكنك إرجاع المنتج خلال 14 يوم من تاريخ الاستلام بشرط أن يكون في حالته الأصلية."}], "fields": [{"type": "string", "icon": "sicon-help-circle", "label": "السؤال", "multilanguage": true, "id": "questions.question", "value": null, "required": true, "format": "text", "description": "نص السؤال الذي سيظهر للمستخدمين", "placeholder": "أدخل السؤال هنا...", "minLength": 5, "maxLength": 200}, {"type": "string", "icon": "sicon-chat-bubble-dots", "label": "الإجابة", "multilanguage": true, "id": "questions.answer", "value": null, "description": "الإجابة التفصيلية على السؤال", "format": "textarea", "required": true, "placeholder": "أدخل الإجابة هنا...", "minLength": 10, "maxLength": 1000}]}, {"type": "string", "icon": "sicon-palette", "label": "لون العنوان الرئيسي", "id": "title_color", "value": "#333333", "required": false, "format": "color", "description": "اختر لون العنوان الرئيسي"}, {"type": "string", "icon": "sicon-palette", "label": "لون أسئلة الـ FAQ", "id": "question_color", "value": "#2563eb", "required": false, "format": "color", "description": "اختر لون نصوص الأسئلة"}]}, {"key": "0bad974f-cb52-4005-a3a5-6e1b6b88b75d", "title": "معرض طولي مميز", "icon": "sicon-add_col_before", "path": "home.specialGallaryGaming", "fields": [{"type": "static", "format": "description", "id": "static-desc", "value": "<div style='padding-top:10px;margin-bottom:30px;border:1px solid #ebebeb;border-radius:5px;text-align:center;overflow:hidden'><h6 style='margin-bottom:10px'>معاينة العنصر</h6><p>معرض مميز طولي يعرض مجموعة من العناصر، كل عنصر يحتوي على صورة وعنوان ووصف وزر رابط</p></div>"}, {"type": "string", "icon": "sicon-format-text-alt", "label": "العنوان الرئيسي", "multilanguage": true, "id": "main_title", "value": {"ar": "معرض مميز", "en": "Special Gallery"}, "required": false, "format": "text", "description": "العنوان الذي سيظهر أعلى المعرض", "placeholder": "أدخل العنوان الرئيسي هنا...", "minLength": 0, "maxLength": 100}, {"type": "string", "icon": "sicon-typography", "label": "نص توضيحي", "multilanguage": true, "id": "main_description", "value": {"ar": "اكتشف مجموعتنا المميزة من المنتجات", "en": "Discover our special collection of products"}, "description": "نص توضيحي يظهر تحت العنوان الرئيسي", "format": "textarea", "required": false, "placeholder": "يمكنك إضافة وصف توضيحي هنا...", "minLength": 0, "maxLength": 255}, {"id": "gallery_items", "type": "collection", "format": "collection", "required": true, "minLength": 1, "maxLength": 10, "label": "عناصر المعرض", "item_label": "عنصر", "value": [{"gallery_items.image": "https://cdn.salla.sa/form-builder/D7bG3pgSit43TheOejBOZV7LIfG1y1Cj0hWLmgIh.jpg", "gallery_items.title": "أكل ما تحتاجه يوميًا", "gallery_items.description": "تجربة طعام فريدة مع أفضل المكونات الطازجة", "gallery_items.button_text": "اطلب الآن", "gallery_items.url__type": "offers_link"}, {"gallery_items.image": "https://cdn.salla.sa/form-builder/D7bG3pgSit43TheOejBOZV7LIfG1y1Cj0hWLmgIh.jpg", "gallery_items.title": "دجاج طازج على الفحم", "gallery_items.description": "دجاج مشوي بنكهة مميزة على الفحم الطبيعي", "gallery_items.button_text": "جر<PERSON> الآن", "gallery_items.url__type": "offers_link"}], "fields": [{"type": "string", "icon": "sicon-image", "value": null, "label": "صورة العنصر", "id": "gallery_items.image", "format": "image", "required": true, "placeholder": null, "description": "* المقاس المناسب للصورة هو 400×300 بكسل", "settings": {"height": 300, "width": 400}}, {"type": "string", "icon": "sicon-format-text-alt", "label": "عنوان العنصر", "multilanguage": true, "id": "gallery_items.title", "value": null, "required": true, "format": "text", "description": "العنوان الذي سيظهر مع العنصر", "placeholder": "أدخل عنوان العنصر هنا...", "minLength": 2, "maxLength": 100}, {"type": "string", "icon": "sicon-typography", "label": "وصف العنصر", "multilanguage": true, "id": "gallery_items.description", "value": null, "description": "وصف مختصر للعنصر", "format": "textarea", "required": false, "placeholder": "أدخل وصف العنصر هنا...", "minLength": 0, "maxLength": 255}, {"type": "string", "icon": "sicon-format-text-alt", "label": "نص الزر", "multilanguage": true, "id": "gallery_items.button_text", "value": {"ar": "اطلب الآن", "en": "Order Now"}, "required": false, "format": "text", "description": "النص الذي سيظهر على الزر", "placeholder": "أدخل نص الزر هنا...", "minLength": 2, "maxLength": 50}, {"type": "items", "icon": "sicon-link", "label": "رابط العنصر", "id": "gallery_items.url", "value": null, "description": "الرابط الذي سيتم فتحه عند النقر على الزر", "required": false, "format": "variable-list", "searchable": true, "source": "custom", "sources": [{"label": "منتج", "key": "products", "value": "products"}, {"label": "تصنيف", "key": "categories", "value": "categories"}, {"label": "ماركة تجارية", "key": "brands", "value": "brands"}, {"label": "صفحة تعريفية", "key": "pages", "value": "pages"}, {"label": "مقالة", "key": "blog_articles", "value": "blog_articles"}, {"label": "تصنيف ض<PERSON>ن المدونة", "key": "blog_categories", "value": "blog_categories"}, {"label": "التخفيضات", "key": "offers_link", "value": "offers_link"}, {"label": "الماركات التجارية", "key": "brands_link", "value": "brands_link"}, {"label": "المدونة", "key": "blog_link", "value": "blog_link"}, {"label": "را<PERSON><PERSON> خارجي", "key": "custom", "value": "custom"}]}]}, {"type": "static", "id": "static-line-1", "format": "line"}, {"type": "static", "format": "title", "id": "static-title-text-styling", "value": "إعدادات النصوص", "variant": "h6"}, {"id": "main_title_font_size", "type": "items", "format": "dropdown-list", "label": "حج<PERSON> خط العنوان الرئيسي", "description": "اخ<PERSON>ر حجم خط العنوان الرئيسي للمعرض", "labelHTML": null, "icon": "sicon-format-text-size", "selected": [{"label": "كبير", "value": "large", "key": "main-title-font-size-large"}], "options": [{"label": "صغير", "value": "small", "key": "main-title-font-size-small"}, {"label": "متوسط", "value": "medium", "key": "main-title-font-size-medium"}, {"label": "كبير", "value": "large", "key": "main-title-font-size-large"}, {"label": "كبير جداً", "value": "extra-large", "key": "main-title-font-size-extra-large"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "main_title_font_weight", "type": "items", "format": "dropdown-list", "label": "سُمك خط العنوان الرئيسي", "description": "اختر سُمك خط العنوان الرئيسي", "labelHTML": null, "icon": "sicon-format-bold", "selected": [{"label": "عريض", "value": "bold", "key": "main-title-font-weight-bold"}], "options": [{"label": "عادي", "value": "normal", "key": "main-title-font-weight-normal"}, {"label": "متوسط", "value": "medium", "key": "main-title-font-weight-medium"}, {"label": "عريض", "value": "bold", "key": "main-title-font-weight-bold"}, {"label": "عريض جداً", "value": "extra-bold", "key": "main-title-font-weight-extra-bold"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "item_title_font_size", "type": "items", "format": "dropdown-list", "label": "حج<PERSON> خط عناوين العناصر", "description": "اخ<PERSON>ر حجم خط عناوين العناصر", "labelHTML": null, "icon": "sicon-format-text-size", "selected": [{"label": "متوسط", "value": "medium", "key": "item-title-font-size-medium"}], "options": [{"label": "صغير", "value": "small", "key": "item-title-font-size-small"}, {"label": "متوسط", "value": "medium", "key": "item-title-font-size-medium"}, {"label": "كبير", "value": "large", "key": "item-title-font-size-large"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "item_title_font_weight", "type": "items", "format": "dropdown-list", "label": "سُمك خط عناوين العناصر", "description": "اختر سُمك خط عناوين العناصر", "labelHTML": null, "icon": "sicon-format-bold", "selected": [{"label": "متوسط", "value": "medium", "key": "item-title-font-weight-medium"}], "options": [{"label": "عادي", "value": "normal", "key": "item-title-font-weight-normal"}, {"label": "متوسط", "value": "medium", "key": "item-title-font-weight-medium"}, {"label": "عريض", "value": "bold", "key": "item-title-font-weight-bold"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "description_font_size", "type": "items", "format": "dropdown-list", "label": "<PERSON>ج<PERSON> خط الوصف", "description": "اختر حجم خط نصوص الوصف", "labelHTML": null, "icon": "sicon-format-text-size", "selected": [{"label": "صغير", "value": "small", "key": "description-font-size-small"}], "options": [{"label": "صغير جداً", "value": "extra-small", "key": "description-font-size-extra-small"}, {"label": "صغير", "value": "small", "key": "description-font-size-small"}, {"label": "متوسط", "value": "medium", "key": "description-font-size-medium"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "text_alignment", "type": "items", "format": "dropdown-list", "label": "محاذاة النصوص", "description": "اختر محاذاة النصوص في المعرض", "labelHTML": null, "icon": "sicon-format-align-center", "selected": [{"label": "وسط", "value": "center", "key": "text-alignment-center"}], "options": [{"label": "يمين", "value": "right", "key": "text-alignment-right"}, {"label": "وسط", "value": "center", "key": "text-alignment-center"}, {"label": "يسار", "value": "left", "key": "text-alignment-left"}], "source": "Manual", "required": false, "multichoice": false}, {"type": "static", "id": "static-line-2", "format": "line"}, {"type": "static", "format": "title", "id": "static-title-colors", "value": "إعدادات الألوان", "variant": "h6"}, {"type": "string", "icon": "sicon-format-fill", "label": "لون العنوان الرئيسي", "description": "اختر لون العنوان الرئيسي للمعرض", "id": "main_title_color", "format": "color", "inputType": "color", "required": false, "value": "#ffffff"}, {"type": "string", "icon": "sicon-format-fill", "label": "لون عناوين العناصر", "description": "اختر لون عناوين العناصر", "id": "item_title_color", "format": "color", "inputType": "color", "required": false, "value": "#ffffff"}, {"type": "string", "icon": "sicon-format-fill", "label": "لون نصوص الوصف", "description": "اختر لون نصوص الوصف", "id": "item_description_color", "format": "color", "inputType": "color", "required": false, "value": "#e0e0e0"}, {"type": "string", "icon": "sicon-format-fill", "label": "لون خلفية الأزرار", "description": "اختر لون خلفية الأزرار", "id": "button_bg_color", "format": "color", "inputType": "color", "required": false, "value": "#00ff88"}, {"type": "string", "icon": "sicon-format-fill", "label": "لون نص الأزرار", "description": "اختر لون نص الأزرار", "id": "button_text_color", "format": "color", "inputType": "color", "required": false, "value": "#000000"}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تفعيل خلفية مخصصة", "description": "تفعيل أو إلغاء الخلفية المخصصة للمعرض", "id": "enable_background", "format": "switch", "required": false, "value": false, "selected": false}, {"type": "string", "icon": "sicon-format-fill", "label": "لون الخلفية", "description": "اختر لون الخلفية (يظهر عند التفعيل)", "id": "background_color", "format": "color", "inputType": "color", "required": false, "value": "#0a0a0a"}, {"type": "string", "icon": "sicon-format-fill", "label": "لون تأثير التمرير", "description": "اختر لون التأثير عند التمرير على العناصر", "id": "hover_overlay_color", "format": "color", "inputType": "color", "required": false, "value": "#00ff88"}, {"type": "string", "icon": "sicon-format-fill", "label": "لون الحدود المضيئة", "description": "اختر لون الحدود المضيئة للعناصر", "id": "glow_border_color", "format": "color", "inputType": "color", "required": false, "value": "#00d4ff"}, {"type": "string", "icon": "sicon-format-fill", "label": "لون خلفية العناصر", "description": "اختر لون خلفية العناصر", "id": "item_background_color", "format": "color", "inputType": "color", "required": false, "value": "#1a1a1a"}, {"type": "static", "id": "static-line-3", "format": "line"}, {"type": "static", "format": "title", "id": "static-title-animations", "value": "إعدادات الحركة", "variant": "h6"}, {"id": "gallery_animation_type", "type": "items", "format": "dropdown-list", "label": "نوع حركة المعرض", "description": "اختر نوع حركة دخول المعرض", "labelHTML": null, "icon": "sicon-refresh", "selected": [{"label": "تلاشي (FadeIn)", "value": "fadeIn", "key": "gallery-animation-type-fadeIn"}], "options": [{"label": "تلاشي (FadeIn)", "value": "fadeIn", "key": "gallery-animation-type-fadeIn"}, {"label": "انزلاق للأعلى (SlideUp)", "value": "slideUp", "key": "gallery-animation-type-slideUp"}, {"label": "تكبير (ScaleIn)", "value": "scaleIn", "key": "gallery-animation-type-scaleIn"}, {"label": "ارتداد (BounceIn)", "value": "bounceIn", "key": "gallery-animation-type-bounceIn"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "item_animation_type", "type": "items", "format": "dropdown-list", "label": "نوع حركة العناصر", "description": "اختر نوع حركة دخول العناصر", "labelHTML": null, "icon": "sicon-refresh", "selected": [{"label": "انزلاق للأعلى (SlideUp)", "value": "slideUp", "key": "item-animation-type-slideUp"}], "options": [{"label": "تلاشي (FadeIn)", "value": "fadeIn", "key": "item-animation-type-fadeIn"}, {"label": "انزلاق للأعلى (SlideUp)", "value": "slideUp", "key": "item-animation-type-slideUp"}, {"label": "تكبير (ScaleIn)", "value": "scaleIn", "key": "item-animation-type-scaleIn"}, {"label": "انزلاق من اليسار (SlideLeft)", "value": "slideLeft", "key": "item-animation-type-slideLeft"}, {"label": "انزلاق من اليمين (SlideRight)", "value": "slideRight", "key": "item-animation-type-slideRight"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "text_animation_type", "type": "items", "format": "dropdown-list", "label": "نوع حركة النصوص", "description": "اختر نوع حركة دخول النصوص", "labelHTML": null, "icon": "sicon-refresh", "selected": [{"label": "توسع الحروف (Tracking Expand)", "value": "trackingExpand", "key": "text-animation-type-trackingExpand"}], "options": [{"label": "تلاشي (FadeIn)", "value": "fadeIn", "key": "text-animation-type-fadeIn"}, {"label": "انزلاق للأعلى (SlideUp)", "value": "slideUp", "key": "text-animation-type-slideUp"}, {"label": "تكبير (ScaleIn)", "value": "scaleIn", "key": "text-animation-type-scaleIn"}, {"label": "توسع الحروف (Tracking Expand)", "value": "trackingExpand", "key": "text-animation-type-trackingExpand"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "animation_duration", "type": "items", "format": "dropdown-list", "label": "مدة الحركة", "description": "اختر مدة تشغيل الحركة", "labelHTML": null, "icon": "sicon-clock", "selected": [{"label": "متوسط (1 ثانية)", "value": "medium", "key": "animation-duration-medium"}], "options": [{"label": "سريع (0.5 ثانية)", "value": "fast", "key": "animation-duration-fast"}, {"label": "متوسط (1 ثانية)", "value": "medium", "key": "animation-duration-medium"}, {"label": "بطيء (1.5 ثانية)", "value": "slow", "key": "animation-duration-slow"}, {"label": "بطيء جداً (2 ثانية)", "value": "extra-slow", "key": "animation-duration-extra-slow"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "animation_delay", "type": "items", "format": "dropdown-list", "label": "تأخير الحركة", "description": "اختر تأخير بدء الحركة", "labelHTML": null, "icon": "sicon-clock", "selected": [{"label": "قصير (0.2 ثانية)", "value": "short", "key": "animation-delay-short"}], "options": [{"label": "بدون تأخير", "value": "none", "key": "animation-delay-none"}, {"label": "قصير (0.2 ثانية)", "value": "short", "key": "animation-delay-short"}, {"label": "متوسط (0.5 ثانية)", "value": "medium", "key": "animation-delay-medium"}, {"label": "طويل (1 ثانية)", "value": "long", "key": "animation-delay-long"}], "source": "Manual", "required": false, "multichoice": false}, {"type": "static", "id": "static-line-4", "format": "line"}, {"type": "static", "format": "title", "id": "static-title-layout", "value": "إعدادات التخطيط", "variant": "h6"}, {"id": "items_per_row", "type": "items", "format": "dropdown-list", "label": "ع<PERSON><PERSON> العناصر في الصف", "description": "ا<PERSON><PERSON>ر عدد العناصر المعروضة في كل صف", "labelHTML": null, "icon": "sicon-grid", "selected": [{"label": "3 عناصر", "value": "3", "key": "items-per-row-3"}], "options": [{"label": "2 عناصر", "value": "2", "key": "items-per-row-2"}, {"label": "3 عناصر", "value": "3", "key": "items-per-row-3"}, {"label": "4 عناصر", "value": "4", "key": "items-per-row-4"}, {"label": "5 عناصر", "value": "5", "key": "items-per-row-5"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "item_spacing", "type": "number", "format": "number", "label": "المسافة بين العناصر", "description": "المسافة بين العناصر بالبكسل", "labelHTML": null, "icon": "sicon-arrow-left-right", "value": 20, "required": false, "min": 10, "max": 50, "step": 5}, {"id": "top_margin", "type": "number", "format": "number", "label": "المسافة العلوية", "description": "المسافة العلوية للمعرض بالبكسل", "labelHTML": null, "icon": "sicon-arrow-up", "value": 0, "required": false, "min": 0, "max": 200, "step": 10}, {"id": "bottom_margin", "type": "number", "format": "number", "label": "المسافة السفلية", "description": "المسافة السفلية للمعرض بالبكسل", "labelHTML": null, "icon": "sicon-arrow-down", "value": 40, "required": false, "min": 0, "max": 200, "step": 10}, {"type": "boolean", "icon": "sicon-expand", "label": "عر<PERSON> كامل", "description": "جعل المعرض يم<PERSON>د عبر العرض الكامل للشاشة", "id": "full_width", "format": "switch", "required": false, "value": false, "selected": false}, {"type": "static", "format": "title", "id": "static-title-responsive", "value": "إعدادات الاستجابة للأجهزة", "variant": "h6"}, {"id": "mobile_items_per_row", "type": "items", "format": "dropdown-list", "label": "عد<PERSON> العناصر في الصف (موباي<PERSON>)", "description": "عد<PERSON> العناصر المعروضة في كل صف على الأجهزة المحمولة", "labelHTML": null, "icon": "sicon-mobile", "selected": [{"label": "عنصر واحد", "value": "1", "key": "mobile-items-1"}], "options": [{"label": "عنصر واحد", "value": "1", "key": "mobile-items-1"}, {"label": "عنصران", "value": "2", "key": "mobile-items-2"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "tablet_items_per_row", "type": "items", "format": "dropdown-list", "label": "عد<PERSON> العناصر في الصف (تاب<PERSON>ت)", "description": "عد<PERSON> العناصر المعروضة في كل صف على أجهزة التابلت", "labelHTML": null, "icon": "sicon-tablet", "selected": [{"label": "عنصران", "value": "2", "key": "tablet-items-2"}], "options": [{"label": "عنصر واحد", "value": "1", "key": "tablet-items-1"}, {"label": "عنصران", "value": "2", "key": "tablet-items-2"}, {"label": "ثلاثة عناصر", "value": "3", "key": "tablet-items-3"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "mobile_spacing", "type": "number", "format": "number", "label": "المسافة بين العناصر (موبايل)", "description": "المسافة بين العناصر على الأجهزة المحمولة بالبكسل", "labelHTML": null, "icon": "sicon-mobile", "value": 15, "required": false, "min": 5, "max": 50, "step": 5}, {"type": "boolean", "icon": "sicon-mobile", "label": "عرض كامل للموبايل", "description": "جعل المعرض يم<PERSON>د عبر العرض الكامل على الأجهزة المحمولة", "id": "enable_mobile_full_width", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "static", "id": "static-line-5", "format": "line"}, {"type": "static", "format": "title", "id": "static-title-visual-effects", "value": "التأثيرات البصرية", "variant": "h6"}, {"type": "boolean", "icon": "sicon-magic-wand", "label": "تفعيل تأثيرات التمرير", "description": "تفعيل التأثيرات البصرية عند التمرير على العناصر", "id": "enable_hover_effects", "format": "switch", "required": false, "value": true, "selected": true}, {"id": "hover_animation_type", "type": "items", "format": "dropdown-list", "label": "نوع حركة التمرير", "description": "اختر نوع الحركة عند التمرير على العناصر", "labelHTML": null, "icon": "sicon-refresh", "selected": [{"label": "تكبير (Scale)", "value": "scale", "key": "hover-animation-type-scale"}], "options": [{"label": "تكبير (Scale)", "value": "scale", "key": "hover-animation-type-scale"}, {"label": "انزلاق (Slide)", "value": "slide", "key": "hover-animation-type-slide"}, {"label": "نبضة (Pulse)", "value": "pulse", "key": "hover-animation-type-pulse"}, {"label": "إضاءة (Glow)", "value": "glow", "key": "hover-animation-type-glow"}, {"label": "رفع (Lift)", "value": "lift", "key": "hover-animation-type-lift"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "image_border_radius", "type": "number", "format": "number", "label": "انحناء زوايا الصور", "description": "مقدار انحناء زوايا الصور بالبكسل", "labelHTML": null, "icon": "sicon-border-radius", "value": 10, "required": false, "min": 0, "max": 50, "step": 5}, {"type": "boolean", "icon": "sicon-shadow", "label": "تفعيل الظلال", "description": "إضافة ظلال للعناصر", "id": "enable_image_shadows", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "boolean", "icon": "sicon-sparkles", "label": "تفعيل التأثيرات المضيئة", "description": "إضافة تأثيرات مضيئة حول العناصر", "id": "enable_glow_effects", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "boolean", "icon": "sicon-sparkles", "label": "تفعيل الجسيمات المتحركة", "description": "إضافة جسيمات متحركة في الخلفية", "id": "enable_particles", "format": "switch", "required": false, "value": false, "selected": false}, {"type": "boolean", "icon": "sicon-image", "label": "تفعيل تأثيرات الصور", "description": "إضافة تأثيرات بصرية للصور عند التحميل", "id": "enable_image_effects", "format": "switch", "required": false, "value": true, "selected": true}]}, {"key": "666a46de-d7c0-4f3d-9c5e-86af97fe98b8", "title": "شريط اعلانات", "icon": "sicon-align-center", "path": "home.movingText", "fields": [{"id": "moving_text", "type": "string", "format": "text", "label": "النص داخل الشريط", "description": null, "labelHTML": null, "placeholder": "اضف نص للعروض", "icon": "sicon-format-text-alt", "value": null, "multilanguage": false, "required": true, "minLength": 6, "maxLength": 30}, {"id": "color", "type": "string", "format": "color", "inputType": "color", "label": "لون الشريط", "description": "اختر لون الشريط", "labelHTML": null, "icon": "sicon-format-fill", "value": "#000000", "required": false}, {"id": "animation_speed", "type": "items", "format": "dropdown-list", "label": "سرعة حركة النص", "description": "اختر سرعة حركة النص", "labelHTML": null, "icon": "sicon-speed", "selected": [{"label": "عادي", "value": "25", "key": "normal-speed"}], "options": [{"label": "سريع جداً", "value": "10", "key": "very-fast-speed"}, {"label": "سريع", "value": "15", "key": "fast-speed"}, {"label": "عادي", "value": "25", "key": "normal-speed"}, {"label": "بطيء", "value": "35", "key": "slow-speed"}, {"label": "بطيء جداً", "value": "50", "key": "very-slow-speed"}], "source": "Manual", "required": false, "multichoice": false}]}, {"key": "8631a835-e424-4509-a882-42a0ac79bf1e", "title": "منتج مميز مع عناوين فرعية", "icon": "sicon-image1", "path": "home.productWithIcons", "fields": [{"id": "title", "type": "string", "format": "text", "label": "عنوان القسم", "description": "العنوان الرئيسي للكومبوننت", "placeholder": "مثال: منتجاتنا المميزة", "icon": "sicon-text", "value": null, "required": false}, {"id": "description", "type": "string", "format": "textarea", "label": "وصف القسم", "description": "وصف مختصر للكومبوننت", "placeholder": "مثال: اكتشف مجموعتنا المختارة من أفضل المنتجات", "icon": "sicon-text-align-right", "value": null, "required": false}, {"id": "main_image", "type": "string", "format": "image", "label": "الصورة الرئيسية", "description": "اختر الصورة التي ستظهر في الوسط", "placeholder": "e.g. https://hostname.com/image.png", "icon": "sicon-image", "value": null, "required": true}, {"id": "main_image_link_type", "type": "string", "format": "dropdown-list", "label": "نوع الرابط", "description": "اختر نوع الرابط للصورة الرئيسية", "icon": "sicon-link", "value": null, "required": false, "source": "custom", "sources": [{"label": "منتج", "key": "products", "value": "products"}, {"label": "تصنيف", "key": "categories", "value": "categories"}, {"label": "ماركة تجارية", "key": "brands", "value": "brands"}, {"label": "صفحة تعريفية", "key": "pages", "value": "pages"}, {"label": "مقالة", "key": "blog_articles", "value": "blog_articles"}, {"label": "تصنيف ض<PERSON>ن المدونة", "key": "blog_categories", "value": "blog_categories"}, {"label": "التخفيضات", "key": "offers_link", "value": "offers_link"}, {"label": "الماركات التجارية", "key": "brands_link", "value": "brands_link"}, {"label": "المدونة", "key": "blog_link", "value": "blog_link"}, {"label": "را<PERSON><PERSON> خارجي", "key": "custom", "value": "custom"}]}, {"id": "main_image_link_value", "type": "items", "format": "dropdown-list", "label": "اختر العنصر", "description": "اختر العنصر المطلوب الربط به", "icon": "sicon-list", "selected": [], "options": [], "required": false, "multichoice": false, "source": "products", "searchable": true, "maxLength": 1, "minLength": 0, "depends_on": "main_image_link_type"}, {"id": "right_icons", "type": "collection", "format": "collection", "label": "أيقونات الجانب الأيمن", "description": "مجموعة الأيقونات التي ستظهر في الجانب الأيمن", "item_label": "أيقونة", "icon": "sicon-list-add", "fields": [{"id": "right_icons.icon", "type": "string", "format": "icon", "label": "الأيقونة", "description": "اختر الأيقونة من القائمة", "icon": "sicon-star", "value": null, "required": true}, {"id": "right_icons.title", "type": "string", "format": "text", "label": "عنوان الأيقونة", "description": "النص الذي سيظهر تحت الأيقونة", "placeholder": "مثال: توصيل سريع", "icon": "sicon-text", "value": null, "required": true}]}, {"id": "left_icons", "type": "collection", "format": "collection", "label": "أيقونات الجانب الأيسر", "description": "مجموعة الأيقونات التي ستظهر في الجانب الأيسر", "item_label": "أيقونة", "icon": "sicon-list-add", "fields": [{"id": "left_icons.icon", "type": "string", "format": "icon", "label": "الأيقونة", "description": "اختر الأيقونة من القائمة", "icon": "sicon-star", "value": null, "required": true}, {"id": "left_icons.title", "type": "string", "format": "text", "label": "عنوان الأيقونة", "description": "النص الذي سيظهر تحت الأيقونة", "placeholder": "مثال: <PERSON><PERSON><PERSON> الجودة", "icon": "sicon-text", "value": null, "required": true}]}]}, {"key": "8e103e12-d089-4736-b33b-5ce3e7548d22", "title": "بنر عصري", "icon": "sicon-image1", "path": "home.WahgBanner", "fields": [{"type": "static", "format": "title", "id": "static-title-content", "value": "إعدادات المحتوى", "variant": "h6"}, {"type": "string", "icon": "sicon-image", "label": "صورة البنر", "id": "banner_image", "format": "image", "required": true, "description": "* المقاس المناسب للصورة هو 1200×400 بكسل", "settings": {"height": 400, "width": 1200}}, {"type": "items", "icon": "sicon-link", "label": "راب<PERSON> البنر", "id": "banner_url", "format": "variable-list", "value": [], "description": "اختر الرابط الذي سيتم التوجه إليه عند النقر على البنر", "required": false, "source": "custom", "sources": [{"label": "منتج", "key": "products", "value": "products"}, {"label": "تصنيف", "key": "categories", "value": "categories"}, {"label": "ماركة تجارية", "key": "brands", "value": "brands"}, {"label": "صفحة تعريفية", "key": "pages", "value": "pages"}, {"label": "مقالة", "key": "blog_articles", "value": "blog_articles"}, {"label": "تصنيف ض<PERSON>ن المدونة", "key": "blog_categories", "value": "blog_categories"}, {"label": "التخفيضات", "key": "offers_link", "value": "offers_link"}, {"label": "الماركات التجارية", "key": "brands_link", "value": "brands_link"}, {"label": "المدونة", "key": "blog_link", "value": "blog_link"}, {"label": "را<PERSON><PERSON> خارجي", "key": "custom", "value": "custom"}], "multichoice": false, "searchable": true}, {"type": "string", "icon": "sicon-format-text-alt", "label": "نص بديل للصورة", "id": "banner_alt", "format": "text", "required": false, "placeholder": "وصف الصورة", "description": "نص بديل يظهر في حالة عدم تحميل الصورة"}, {"type": "static", "format": "title", "id": "static-title-display", "value": "خيارات العرض", "variant": "h6"}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "عرض بعرض الشاشة الكامل", "description": "جعل الب<PERSON><PERSON> يم<PERSON>د عبر عرض الشاشة الكامل خارج الحاوية", "id": "full_width", "format": "switch", "required": false, "value": false, "selected": false}, {"id": "banner_height", "type": "number", "format": "integer", "inputType": "number", "label": "ارتفاع البنر", "description": "ارتفاع البنر بالبكسل", "labelHTML": null, "placeholder": "أدخل ارتفاع البنر (200-600 بكسل)", "icon": "sicon-arrow-up-down", "value": 400, "required": false, "minimum": 200, "maximum": 600}, {"id": "border_radius", "type": "number", "format": "integer", "inputType": "number", "label": "انحناء الحواف", "description": "انحناء حواف البنر بالبكسل", "labelHTML": null, "placeholder": "أدخل انحناء الحواف (0-50 بكسل)", "icon": "sicon-border-radius", "value": 12, "required": false, "minimum": 0, "maximum": 50}, {"type": "static", "format": "title", "id": "static-title-hover", "value": "تأثيرات التفاعل", "variant": "h6"}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تفعيل تأثيرات التفاعل", "description": "إضافة تأثيرات عند تمرير الماوس فوق البنر", "id": "enable_hover_effects", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "string", "icon": "sicon-palette", "label": "لون طبقة التفاعل", "id": "hover_overlay_color", "value": "#000000", "required": false, "format": "color", "description": "اختر لون الطبقة التي تظهر عند التفاعل"}, {"id": "hover_overlay_opacity", "type": "number", "format": "integer", "inputType": "number", "label": "شفافية طبقة التفاعل", "description": "شفافية الطبقة عند التفاعل (0-100%)", "labelHTML": null, "placeholder": "أدخل نسبة الشفافية (0-100)", "icon": "sicon-opacity", "value": 20, "required": false, "minimum": 0, "maximum": 100}, {"id": "animation_type", "type": "items", "format": "dropdown-list", "label": "نوع الحركة", "description": "اختر نوع الحركة عند التفاعل", "labelHTML": null, "icon": "sicon-refresh", "selected": [{"label": "تكبير (Zoom)", "value": "zoom", "key": "animation-type-zoom"}], "options": [{"label": "تلاشي (Fade)", "value": "fade", "key": "animation-type-fade"}, {"label": "تكبير (Zoom)", "value": "zoom", "key": "animation-type-zoom"}, {"label": "انزلاق (Slide)", "value": "slide", "key": "animation-type-slide"}, {"label": "بدون حركة", "value": "none", "key": "animation-type-none"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "animation_duration", "type": "items", "format": "dropdown-list", "label": "سرعة الحركة", "description": "اختر سرعة الحركة والتأثيرات", "labelHTML": null, "icon": "sicon-speed", "selected": [{"label": "عادي", "value": "normal", "key": "animation-duration-normal"}], "options": [{"label": "بطيء", "value": "slow", "key": "animation-duration-slow"}, {"label": "عادي", "value": "normal", "key": "animation-duration-normal"}, {"label": "سريع", "value": "fast", "key": "animation-duration-fast"}], "source": "Manual", "required": false, "multichoice": false}, {"type": "static", "format": "title", "id": "static-title-visual", "value": "التحسينات البصرية", "variant": "h6"}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تفعيل الظل", "description": "إضافة ظل حول البنر", "id": "enable_shadow", "format": "switch", "required": false, "value": true, "selected": true}, {"type": "string", "icon": "sicon-palette", "label": "لون الظل", "id": "shadow_color", "value": "#000000", "required": false, "format": "color", "description": "اختر لون الظل"}, {"id": "shadow_intensity", "type": "number", "format": "integer", "inputType": "number", "label": "قوة الظل", "description": "قوة الظل بالبكسل (0-20)", "labelHTML": null, "placeholder": "أدخل قوة الظل (0-20 بكسل)", "icon": "sicon-shadow", "value": 10, "required": false, "minimum": 0, "maximum": 20}, {"type": "boolean", "icon": "sicon-toggle-off", "label": "تفعيل الحدود", "description": "إضافة حدود حول البنر", "id": "enable_border", "format": "switch", "required": false, "value": false, "selected": false}, {"type": "string", "icon": "sicon-palette", "label": "لون الحدود", "id": "border_color", "value": "#e5e7eb", "required": false, "format": "color", "description": "اختر لون الحدود"}, {"type": "static", "description": "دمج هذا الكومبوننت مع الكومبوننت الذي يعلوه", "id": "merge_with_top_component", "format": "switch", "required": false, "value": false}, {"id": "categories", "type": "collection", "format": "collection", "required": true, "minLength": 1, "maxLength": 20, "label": "التصنيفات", "item_label": "تصنيف", "value": [{"categories.image": "https://cdn.salla.sa/form-builder/D7bG3pgSit43TheOejBOZV7LIfG1y1Cj0hWLmgIh.jpg", "categories.name": "إلكترونيات", "categories.url__type": "categories"}, {"categories.image": "https://cdn.salla.sa/form-builder/D7bG3pgSit43TheOejBOZV7LIfG1y1Cj0hWLmgIh.jpg", "categories.name": "ملابس", "categories.url__type": "categories"}, {"categories.image": "https://cdn.salla.sa/form-builder/D7bG3pgSit43TheOejBOZV7LIfG1y1Cj0hWLmgIh.jpg", "categories.name": "منزل ومطبخ", "categories.url__type": "categories"}], "fields": [{"id": "categories.image", "type": "string", "format": "image", "label": "صورة التصنيف", "description": "اختر صورة للتصنيف", "placeholder": "e.g. https://hostname.com/image.png", "icon": "sicon-image", "value": null, "required": true}, {"id": "categories.name", "type": "string", "format": "text", "label": "اسم التصنيف", "description": "اسم التصنيف الذي سيظهر للزوار", "placeholder": "مثال: إلكترونيات", "icon": "sicon-format-text-alt", "value": null, "required": true}, {"type": "items", "icon": "sicon-link", "label": "رابط التصنيف", "id": "categories.url", "value": [], "description": "الرابط الذي سيتم الانتقال إليه عند النقر على التصنيف", "required": true, "format": "variable-list", "searchable": true, "source": "custom", "sources": [{"label": "تصنيف", "key": "categories", "value": "categories"}, {"label": "منتج", "key": "products", "value": "products"}, {"label": "ماركة تجارية", "key": "brands", "value": "brands"}, {"label": "صفحة تعريفية", "key": "pages", "value": "pages"}, {"label": "مقالة", "key": "blog_articles", "value": "blog_articles"}, {"label": "تصنيف ض<PERSON>ن المدونة", "key": "blog_categories", "value": "blog_categories"}, {"label": "التخفيضات", "key": "offers_link", "value": "offers_link"}, {"label": "الماركات التجارية", "key": "brands_link", "value": "brands_link"}, {"label": "المدونة", "key": "blog_link", "value": "blog_link"}, {"label": "را<PERSON><PERSON> خارجي", "key": "custom", "value": "custom"}], "multichoice": false}]}]}, {"key": "7cf4ad7e-8202-4879-b307-fcb02bebafdd", "title": "خريطة المتجر", "icon": "sicon-store", "path": "home.storeMap", "fields": [{"type": "static", "format": "title", "id": "static-title-content", "value": "إعدادات المحتوى", "variant": "h6"}, {"id": "main_title", "type": "string", "format": "text", "label": "العنوان الرئيسي", "description": "العنوان الذي سيظهر أعلى خريطة المتجر", "placeholder": "مثال: مواقع فروعنا", "icon": "sicon-format-text-alt", "value": "مواقع فروعنا", "multilanguage": true, "required": false, "minLength": 2, "maxLength": 100}, {"id": "description", "type": "string", "format": "textarea", "label": "الوصف", "description": "وصف مختصر عن فروع المتجر", "placeholder": "مثال: تفضل بزيارة أحد فروعنا المنتشرة في جميع أنحاء المملكة", "icon": "sicon-chat-bubble-dots", "value": "تفضل بزيارة أحد فروعنا المنتشرة في جميع أنحاء المملكة", "multilanguage": true, "required": false, "minLength": 10, "maxLength": 500}, {"id": "store_branches", "type": "collection", "format": "collection", "required": false, "minLength": 1, "maxLength": 10, "label": "فروع المتجر", "item_label": "فرع", "description": "أضف معلومات فروع المتجر مع خرائط جوجل", "value": [{"store_branches.branch_name": "الفرع الرئيسي", "store_branches.branch_address": "الرياض، حي الملك فهد، شارع الملك عبدالعزيز", "store_branches.google_maps_embed": "<iframe src=\"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3624.140396527!2d46.6753!3d24.7136!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjTCsDQyJzQ5LjAiTiA0NsKwNDAnMzEuMSJF!5e0!3m2!1sen!2ssa!4v1234567890\" width=\"600\" height=\"450\" style=\"border:0;\" allowfullscreen=\"\" loading=\"lazy\" referrerpolicy=\"no-referrer-when-downgrade\"></iframe>"}], "fields": [{"type": "string", "icon": "sicon-store", "label": "اسم الفرع", "multilanguage": true, "id": "store_branches.branch_name", "value": null, "required": true, "format": "text", "description": "اسم الفرع أو المنطقة", "placeholder": "مثال: فرع الرياض الرئيسي", "minLength": 2, "maxLength": 100}, {"type": "string", "icon": "sicon-location", "label": "عنوان الفرع", "multilanguage": true, "id": "store_branches.branch_address", "value": null, "description": "العنوان التفصيلي للفرع", "format": "textarea", "required": true, "placeholder": "مثال: الرياض، حي الملك فهد، شارع الملك عبدالعزيز، مبنى رقم 123", "minLength": 10, "maxLength": 300}, {"type": "string", "icon": "sicon-map", "label": "كو<PERSON> خريطة جوجل", "id": "store_branches.google_maps_embed", "value": null, "description": "الصق هنا كود الخريطة الكامل من جوجل ماب (if<PERSON>e كامل)", "format": "textarea", "required": true, "placeholder": "الصق كود iframe الكامل من Google Maps هنا...", "minLength": 50, "maxLength": 2000}]}, {"type": "static", "format": "line", "id": "static-line-display"}, {"type": "static", "format": "title", "id": "static-title-display", "value": "إعدادات العرض", "variant": "h6"}, {"id": "layout_style", "type": "items", "format": "dropdown-list", "label": "نمط التخطيط", "description": "اختر كيفية عرض الفروع والخرائط", "labelHTML": null, "icon": "sicon-layout-grid", "selected": [{"label": "شبكة (Grid)", "value": "grid", "key": "layout-style-grid"}], "options": [{"label": "شبكة (Grid)", "value": "grid", "key": "layout-style-grid"}, {"label": "قائمة عمودية", "value": "list", "key": "layout-style-list"}, {"label": "تبويبات (Tabs)", "value": "tabs", "key": "layout-style-tabs"}], "source": "Manual", "required": false, "multichoice": false}, {"id": "map_height", "type": "number", "format": "integer", "inputType": "number", "label": "ارتفاع الخريطة", "description": "ارتفاع الخريطة بالبكسل", "placeholder": "400", "icon": "sicon-resize-vertical", "value": 400, "required": false, "minimum": 200, "maximum": 800}, {"id": "columns_desktop", "type": "number", "format": "integer", "inputType": "number", "label": "عدد الأعمدة في الديسكتوب", "description": "عد<PERSON> الأعمدة في الشاشات الكبيرة (نمط الشبكة فقط)", "placeholder": "2", "icon": "sicon-layout-grid", "value": 2, "required": false, "minimum": 1, "maximum": 4}, {"id": "show_branch_info", "type": "boolean", "format": "switch", "label": "إظهار معلومات الفرع", "description": "إظهار اسم وعنوان الفرع مع الخريطة", "icon": "sicon-info-circle", "value": true, "required": false}]}], "support_url": "https://ehaber12.github.io/wh_official/", "description": {"ar": null, "en": null}}