/* Store Map Component - Gaming Theme */
:root {
  --store-map-primary: #1DE9B6;
  --store-map-primary-rgb: 29, 233, 182;
  --store-map-secondary: #FF6B6B;
  --store-map-dark: #121212;
  --store-map-dark-light: #1a1a1a;
  --store-map-text: #ffffff;
  --store-map-text-muted: #b0b0b0;
  --store-map-border: rgba(29, 233, 182, 0.2);
  --store-map-glow: 0 0 20px rgba(29, 233, 182, 0.3);
  --store-map-glow-hover: 0 0 30px rgba(29, 233, 182, 0.5);
}

.s-block--store-map {
  padding: 4rem 0;
  background: var(--store-map-dark);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 80%, rgba(29, 233, 182, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 107, 107, 0.1) 0%, transparent 50%);
    pointer-events: none;
  }

  .container {
    position: relative;
    z-index: 1;
  }
}

/* Header Styles */
.store-map-header {
  margin-bottom: 3rem;
  
  .store-map-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--store-map-text);
    margin-bottom: 1rem;
    text-shadow: var(--store-map-glow);
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 3px;
      background: linear-gradient(90deg, var(--store-map-primary), var(--store-map-secondary));
      border-radius: 2px;
      box-shadow: var(--store-map-glow);
    }
  }
  
  .store-map-description {
    font-size: 1.1rem;
    color: var(--store-map-text-muted);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }
}

/* Grid Layout */
.store-map-grid {
  display: grid;
  gap: 2rem;
  grid-template-columns: repeat(var(--columns, 2), 1fr);
  
  &[data-columns="1"] { --columns: 1; }
  &[data-columns="2"] { --columns: 2; }
  &[data-columns="3"] { --columns: 3; }
  &[data-columns="4"] { --columns: 4; }
}

/* List Layout */
.store-map-list {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

/* Branch Item Styles */
.branch-item {
  background: var(--store-map-dark-light);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid var(--store-map-border);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(29, 233, 182, 0.1), transparent);
    transition: left 0.5s ease;
  }
  
  &:hover {
    border-color: var(--store-map-primary);
    box-shadow: var(--store-map-glow);
    transform: translateY(-5px);
    
    &::before {
      left: 100%;
    }
  }
}

/* Branch Info Styles */
.branch-info {
  margin-bottom: 1rem;
  
  .branch-name {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--store-map-primary);
    margin-bottom: 0.5rem;
    text-shadow: 0 0 10px rgba(29, 233, 182, 0.3);
  }
  
  .branch-address {
    color: var(--store-map-text-muted);
    font-size: 0.95rem;
    line-height: 1.5;
    margin: 0;
  }
}

/* Map Container */
.map-container {
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid var(--store-map-border);
  transition: border-color 0.3s ease;
  position: relative;

  &:hover {
    border-color: var(--store-map-primary);
  }

  .map-embed-wrapper {
    width: 100%;
    height: 100%;

    iframe {
      width: 100%;
      height: 100%;
      border: none;
      display: block;
    }
  }

  .map-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: var(--store-map-dark-light);
    color: var(--store-map-text-muted);
    text-align: center;

    i {
      font-size: 3rem;
      color: var(--store-map-primary);
      margin-bottom: 1rem;
      opacity: 0.7;
    }

    p {
      margin: 0;
      font-size: 1rem;
    }
  }
}

/* No Branches Placeholder */
.no-branches-placeholder {
  text-align: center;
  padding: 4rem 2rem;

  .placeholder-content {
    max-width: 400px;
    margin: 0 auto;

    i {
      font-size: 4rem;
      color: var(--store-map-primary);
      margin-bottom: 1.5rem;
      display: block;
      opacity: 0.7;
    }

    h3 {
      font-size: 1.5rem;
      color: var(--store-map-text);
      margin-bottom: 1rem;
      font-weight: 600;
    }

    p {
      color: var(--store-map-text-muted);
      font-size: 1rem;
      line-height: 1.6;
      margin: 0;
    }
  }
}

/* Tabs Layout */
.store-map-tabs {
  .tabs-navigation {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 2rem;
    justify-content: center;
    
    .tab-button {
      padding: 0.75rem 1.5rem;
      background: var(--store-map-dark-light);
      color: var(--store-map-text-muted);
      border: 1px solid var(--store-map-border);
      border-radius: 25px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 500;
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: radial-gradient(circle, var(--store-map-primary) 0%, transparent 70%);
        transition: all 0.3s ease;
        transform: translate(-50%, -50%);
        opacity: 0;
      }
      
      &:hover, &.active {
        color: var(--store-map-text);
        border-color: var(--store-map-primary);
        box-shadow: var(--store-map-glow);
        
        &::before {
          width: 100%;
          height: 100%;
          opacity: 0.1;
        }
      }
      
      &.active {
        background: rgba(29, 233, 182, 0.1);
        color: var(--store-map-primary);
      }
    }
  }
  
  .tabs-content {
    .tab-panel {
      display: none;
      
      &.active {
        display: block;
        animation: fadeInUp 0.5s ease;
      }
    }
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .store-map-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
  
  .store-map-header .store-map-title {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .s-block--store-map {
    padding: 2rem 0;
  }
  
  .store-map-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .store-map-header {
    margin-bottom: 2rem;
    
    .store-map-title {
      font-size: 1.8rem;
    }
    
    .store-map-description {
      font-size: 1rem;
    }
  }
  
  .branch-item {
    padding: 1rem;
  }
  
  .tabs-navigation {
    .tab-button {
      padding: 0.6rem 1rem;
      font-size: 0.9rem;
    }
  }
}

@media (max-width: 480px) {
  .store-map-header .store-map-title {
    font-size: 1.5rem;
  }
  
  .branch-info .branch-name {
    font-size: 1.1rem;
  }
  
  .tabs-navigation {
    flex-direction: column;
    align-items: center;
    
    .tab-button {
      width: 100%;
      max-width: 250px;
      text-align: center;
    }
  }
}

/* Loading Animation */
.map-container.loading {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    border: 3px solid var(--store-map-border);
    border-top: 3px solid var(--store-map-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
  }
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Error State Styling */
.map-container.error {
  background: var(--store-map-dark-light);
  display: flex;
  align-items: center;
  justify-content: center;

  .error-message {
    text-align: center;
    color: var(--store-map-text-muted);

    i {
      font-size: 2rem;
      color: var(--store-map-secondary);
      margin-bottom: 1rem;
      display: block;
    }

    p {
      margin: 0.5rem 0;
      font-weight: 500;
    }

    small {
      opacity: 0.7;
    }
  }
}

/* Enhanced Hover Effects */
.branch-item:hover .map-container {
  transform: scale(1.02);
  transition: transform 0.3s ease;
}

/* Accessibility Improvements */
.tab-button:focus {
  outline: 2px solid var(--store-map-primary);
  outline-offset: 2px;
}

.map-container:focus-within {
  outline: 2px solid var(--store-map-primary);
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .s-block--store-map {
    background: white !important;
    color: black !important;

    &::before {
      display: none;
    }
  }

  .store-map-title {
    color: black !important;
    text-shadow: none !important;
  }

  .branch-name {
    color: black !important;
    text-shadow: none !important;
  }

  .map-container {
    border: 1px solid #ccc !important;
    page-break-inside: avoid;
  }
}

/* Layout Styles */
.store-branches-container {
  &[data-layout="grid"] {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
  }

  &[data-layout="list"] {
    display: flex;
    flex-direction: column;
    gap: 3rem;

    .branch-item {
      display: grid;
      grid-template-columns: 1fr 2fr;
      gap: 2rem;
      align-items: start;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 1rem;
      }
    }
  }
}

/* Tabs Layout Styles */
.store-branches-tabs {
  .tabs-navigation {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;

    .tab-button {
      padding: 0.75rem 1.5rem;
      background: var(--store-map-dark-light);
      border: 2px solid var(--store-map-border);
      border-radius: 8px;
      color: var(--store-map-text-muted);
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(29, 233, 182, 0.2), transparent);
        transition: left 0.5s ease;
      }

      &:hover {
        border-color: var(--store-map-primary);
        color: var(--store-map-primary);
        box-shadow: var(--store-map-glow);

        &::before {
          left: 100%;
        }
      }

      &.active {
        background: var(--store-map-primary);
        border-color: var(--store-map-primary);
        color: var(--store-map-dark);
        box-shadow: var(--store-map-glow-hover);
      }

      @media (max-width: 768px) {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
      }
    }
  }

  .tabs-content {
    .tab-panel {
      display: none;
      animation: fadeInUp 0.5s ease;

      &.active {
        display: block;
      }
    }
  }
}

/* Animation Keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
